#!/usr/bin/env python3
"""
Test script for the modified inference_diffusion.py
"""

import subprocess
import sys
import os

def test_inference():
    """Test the modified inference script with your specified model."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return False
    
    print(f"✅ Model path exists: {model_path}")
    
    # Run inference with small parameters for testing
    cmd = [
        "python", "scripts/inference_diffusion.py",
        "--model_path", model_path,
        "--num_sequences", "2",  # Small number for testing
        "--max_length", "1024",  # Smaller for faster testing
        "--max_steps", "100",    # Fewer steps for testing
        "--temperature", "0.8",
        "--output_file", "test_inference_results.json",
        "--print_steps"  # Enable step printing for debugging
    ]
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 minute timeout
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Inference completed successfully!")
            
            # Check if output files were created
            if os.path.exists("test_inference_results.json"):
                print("✅ Main output file created")
                
                # Show a preview of the output
                with open("test_inference_results.json", 'r') as f:
                    content = f.read()
                    print(f"📄 Output preview (first 500 chars):")
                    print(content[:500])
                    if len(content) > 500:
                        print("...")
            
            if os.path.exists("test_inference_results_detailed.json"):
                print("✅ Detailed output file created")
            
            return True
        else:
            print(f"❌ Inference failed with return code: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Inference timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running inference: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing modified inference script...")
    success = test_inference()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
        sys.exit(1)
