#!/bin/bash

# Script to generate restructured training data with multiple seeds
# Each seed's data will be combined into train/val/test files

echo "Running restructure_training_data.py with 10 seeds..."

python scripts/restructure_training_data.py \
    --fasta ./data/uniprot_sprot_head1000.fasta \
    --output-dir ./data/CreateProtein_restructured_10seeds \
    --train-ratio 0.7 \
    --test-ratio 0.1 \
    --val-ratio 0.2 \
    --seed 42 \
    --num-seeds 10 \
    --correct-action-prob 0.6 \
    --temperature 1.2 \
    --add-weight 0.4 \
    --delete-weight 0.3 \
    --replace-weight 0.3 \
    --adaptive-correction \
    --min-correct-prob 0.6 \
    --max-correct-prob 0.95 \
    --replace-prob 0.5 \
    --add-prob 0.4

