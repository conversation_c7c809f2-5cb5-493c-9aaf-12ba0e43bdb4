#!/bin/bash

# Script to generate restructured training data with multiple seeds
# and save everything in a combined JSON file

echo "Running restructure_training_data.py with 10 seeds..."

python scripts/restructure_training_data.py \
    --fasta ./data/uniprot_sprot_head1000.fasta \
    --output-dir ./data/CreateProtein_restructured_1seeds \
    --train-ratio 0.7 \
    --test-ratio 0.1 \
    --val-ratio 0.2 \
    --seed 42 \
    --num-seeds 1 \
    --save-combined \
    --correct-action-prob 0.6 \
    --temperature 1.2 \
    --add-weight 0.4 \
    --delete-weight 0.3 \
    --replace-weight 0.3 \
    --adaptive-correction \
    --min-correct-prob 0.6 \
    --max-correct-prob 0.95 \
    --replace-prob 0.5 \
    --add-prob 0.4

echo "Data generation completed!"
echo "Check the output directory: ./data/CreateProtein_restructured_10seeds"
echo "Combined data file: ./data/CreateProtein_restructured_10seeds/combined_data_restructured.json"
