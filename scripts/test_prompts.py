#!/usr/bin/env python3
"""
Test different prompts to find the best one for generating correct action format.
"""

import os
import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM


def test_prompts():
    """Test different prompts to see which works best."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🧪 Testing Different Prompts")
    print("=" * 40)
    
    # Load model
    print("Loading model...")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch.float16,
        device_map="auto"
    )
    model.eval()
    print("✅ Model loaded")
    
    # Test different prompts
    prompts = [
        # Prompt 1: Simple
        "add K at position 0\nadd",
        
        # Prompt 2: With example
        "add K at position 0\nadd C at position 1\nadd",
        
        # Prompt 3: With multiple examples
        "add K at position 0\nadd C at position 1\nremove K from position 0\nadd",
        
        # Prompt 4: Training data style
        "Diffusion reverse process:\nInitial state: ''\nadd K at position 0\nadd C at position 1\nadd",
        
        # Prompt 5: Very explicit
        "add K at position 0\nadd C at position 1\nadd V at position 2\nadd",
        
        # Prompt 6: With position pattern
        "add K at position 0\nadd C at position 1\nadd V at position 2\nadd R at position 3\nadd",
    ]
    
    for i, prompt in enumerate(prompts):
        prompt_display = prompt.replace('\n', '\\n')
        print(f"\n🧪 Test {i+1}: Prompt = '{prompt_display}'")

        inputs = tokenizer(prompt, return_tensors="pt").to("cuda")
        
        try:
            start_time = time.time()
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=25,  # Very small
                    temperature=0.7,
                    top_p=0.8,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                    repetition_penalty=1.3,
                )
            
            gen_time = time.time() - start_time
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"   ✅ Generated in {gen_time:.2f}s")
            print(f"   📝 Full text: '{generated_text}'")
            
            # Extract just the new part
            new_part = generated_text[len(prompt):].strip()
            print(f"   🆕 New part: '{new_part}'")
            
            # Check if it looks like a valid action
            if any(word in new_part.lower() for word in ['at position', 'from position']):
                print(f"   ✅ Contains position-based action")
            else:
                print(f"   ❌ No position-based action found")
            
            # Check if it's too long (likely a protein sequence)
            if len(new_part) > 30:
                print(f"   ⚠️  Generated text is long (likely protein sequence)")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    print(f"\n🎯 Summary: Test completed")


if __name__ == "__main__":
    test_prompts()
