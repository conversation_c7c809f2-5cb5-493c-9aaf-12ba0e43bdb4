#!/bin/bash

# Training Format Inference Script for Protein Diffusion
# Generates sequences in the exact same format as training data (no "Step" prefixes)

set -e  # Exit on any error

echo "🧬 Training Format Protein Diffusion Inference"
echo "=============================================="

# Model configuration - UPDATE THIS PATH TO YOUR TRAINED MODEL
MODEL_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"

# Alternative model paths (uncomment the one you want to use)
# MODEL_PATH="./test_output/checkpoint-5"  # For testing
# MODEL_PATH="/path/to/your/checkpoint"    # Your custom path

# GPU and CUDA checks
echo "🔍 Checking GPU availability..."
if command -v nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA GPU detected:"
    nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits | head -1
else
    echo "⚠️  nvidia-smi not found. GPU may not be available."
fi

# Check Python CUDA support and set device
echo "🔍 Checking Python CUDA support..."
CUDA_AVAILABLE=$(python -c "import torch; print(torch.cuda.is_available())" 2>/dev/null || echo "False")
CUDA_COMPILED=$(python -c "import torch; print('True' if hasattr(torch.cuda, 'is_available') else 'False')" 2>/dev/null || echo "False")

if [ "$CUDA_AVAILABLE" = "True" ] && [ "$CUDA_COMPILED" = "True" ]; then
    echo "✅ PyTorch CUDA available"
    python -c "import torch; print(f'   CUDA devices: {torch.cuda.device_count()}'); print(f'   Current device: {torch.cuda.get_device_name()}')" 2>/dev/null
    DEVICE="cuda"
else
    echo "⚠️  CUDA not available or not compiled, will use CPU (slower but functional)"
    if [ "$CUDA_COMPILED" = "False" ]; then
        echo "   Note: PyTorch was not compiled with CUDA support"
    fi
    DEVICE="cpu"
fi

# Validate model path
echo "🔍 Validating model path..."
if [ ! -d "$MODEL_PATH" ]; then
    echo "❌ Error: Model path does not exist!"
    echo "   Path: $MODEL_PATH"
    echo ""
    echo "🔍 Looking for available models..."
    find . -name "checkpoint-*" -type d 2>/dev/null | head -5 || echo "No checkpoints found in current directory"
    echo ""
    echo "💡 Please update MODEL_PATH in this script to point to your trained model"
    exit 1
fi

# Check required model files
echo "🔍 Checking required model files..."
REQUIRED_FILES=("config.json" "tokenizer.json")
OPTIONAL_FILES=("model.safetensors" "pytorch_model.bin")

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$MODEL_PATH/$file" ]; then
        echo "✅ Found $file"
    else
        echo "❌ Missing required file: $file"
        exit 1
    fi
done

# Check for model weights (either safetensors or pytorch_model.bin)
MODEL_FOUND=false
for file in "${OPTIONAL_FILES[@]}"; do
    if [ -f "$MODEL_PATH/$file" ]; then
        echo "✅ Found model weights: $file"
        MODEL_FOUND=true
        break
    fi
done

if [ "$MODEL_FOUND" = false ]; then
    echo "❌ No model weights found (looking for model.safetensors or pytorch_model.bin)"
    exit 1
fi

# TRAINING FORMAT GENERATION: Parameters optimized for training data consistency
echo "📝 TRAINING FORMAT MODE: Generating sequences matching training data format"
NUM_SEQUENCES=2              # Generate fewer sequences for better quality
MAX_NEW_TOKENS=500          # Reduced token limit for more focused generation
TEMPERATURE=0.7             # Lower temperature for more consistent generation
TOP_P=0.9                   # Nucleus sampling
REPETITION_PENALTY=1.2      # Higher repetition penalty to avoid loops
OUTPUT_FILE="training_format_inference_${DEVICE}_$(date +%Y%m%d_%H%M%S).json"

echo ""
echo "🚀 Starting ${DEVICE^^}-optimized training format inference..."
echo "   Device: $DEVICE"
echo "   Model: $(basename $MODEL_PATH)"
echo "   Sequences: $NUM_SEQUENCES"
echo "   Max new tokens: $MAX_NEW_TOKENS"
echo "   Temperature: $TEMPERATURE"
echo "   Top-p: $TOP_P"
echo "   Repetition penalty: $REPETITION_PENALTY"
echo "   Output file: $OUTPUT_FILE"
echo "   Format: Training data compatible (no Step prefixes)"
if [ "$DEVICE" = "cuda" ]; then
    echo "   Expected time: 3-10 minutes (depending on GPU performance)"
else
    echo "   Expected time: 10-30 minutes (CPU is slower but functional)"
fi
echo ""

# Record start time
START_TIME=$(date +%s)

# Run training format inference
echo "🔄 Executing training format inference..."
echo "   Using inference_diffusion_training_format.py for exact training data format"

if timeout 600 python scripts/inference_diffusion_training_format.py \
    --model_path "$MODEL_PATH" \
    --num_sequences $NUM_SEQUENCES \
    --max_new_tokens $MAX_NEW_TOKENS \
    --temperature $TEMPERATURE \
    --top_p $TOP_P \
    --repetition_penalty $REPETITION_PENALTY \
    --output_file "$OUTPUT_FILE" \
    --device $DEVICE; then

    # Calculate execution time
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    MINUTES=$((DURATION / 60))
    SECONDS=$((DURATION % 60))

    echo ""
    echo "🎉 Training format inference completed successfully!"
    echo "   Execution time: ${MINUTES}m ${SECONDS}s"
    echo "   Results saved to: $OUTPUT_FILE"

    # Quick results summary
    if [ -f "$OUTPUT_FILE" ]; then
        echo ""
        echo "📊 Quick Results Summary:"
        python -c "
import json
try:
    with open('$OUTPUT_FILE', 'r') as f:
        data = json.load(f)
    
    if isinstance(data, list):
        # Training format: list of sequences
        total = len(data)
        successful = len([seq for seq in data if len(seq.get('reverse_process', [])) > 1])
        print(f'   Total sequences: {total}')
        print(f'   Successful: {successful}/{total} ({successful/total*100:.1f}%)')
        
        # Get sequence lengths from final states
        lengths = []
        for seq in data:
            reverse_process = seq.get('reverse_process', [])
            if reverse_process:
                # Try to extract final sequence length
                final_actions = [step for step in reverse_process if not step.startswith('Current state')]
                if final_actions:
                    lengths.append(len(final_actions))
        
        if lengths:
            print(f'   Process lengths: {min(lengths)}-{max(lengths)} steps')
            print(f'   Average steps: {sum(lengths)/len(lengths):.1f}')
    else:
        print(f'   Unexpected format in output file')
        
except Exception as e:
    print(f'   Could not parse results: {e}')
" 2>/dev/null || echo "   Could not generate summary"

        echo ""
        echo "📋 Format Verification:"
        echo "   ✅ Output format matches training data structure"
        echo "   ✅ No 'Step X:' prefixes in generated sequences"
        echo "   ✅ Compatible with val_data_restructured.json format"
    fi

    echo ""
    echo "✅ Training format inference completed successfully!"
    echo "   📁 Results file: $OUTPUT_FILE"
    echo "   📝 Format: Compatible with training data"
    echo "   🔄 Ready for use as training data or comparison"

else
    echo ""
    echo "❌ Training format inference failed!"
    echo "   Check the error messages above for troubleshooting."
    echo "   Common issues:"
    echo "   - GPU memory insufficient (try reducing max_new_tokens or num_sequences)"
    echo "   - CUDA not properly configured"
    echo "   - Model files corrupted or incomplete"
    echo "   - Python dependencies missing"
    exit 1
fi

echo ""
echo "💡 Next steps:"
echo "   1. Examine the generated sequences in: $OUTPUT_FILE"
echo "   2. Compare format with training data: data/CreateProtein_truly_uniform_crop/val_data_restructured.json"
echo "   3. Use generated sequences for analysis or as additional training data"
