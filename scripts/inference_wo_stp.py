#!/usr/bin/env python3
"""
Inference script for the Diffusion‑via‑Reasoning model.

* Loads a trained causal‑language model.
* Autoregressively samples a **reverse‑process** trajectory that edits an
  empty string into a protein sequence.
* Replays those edits with the same helper utilities you used during
  training (`TrajectoryStep`, `apply_step`) so that the final sequence
  exactly matches the actions.
* Produces a JSON summary of all generated sequences.

This version **matches the format of your training data**:
    add X at position Y
    remove X from position Y
    replace A at position Y with B
    Current state: '...'
There are **no explicit “Step N:” prefixes** in the text the model is
asked to emit, but the parser is still robust to them (so your old
checkpoints continue to work).
"""

from __future__ import annotations

import argparse
import json
import os
import re
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Make local src/ importable (for Trajectory utilities)
ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT.parent / "src"))

from diffusion_via_reasoning.trajectory.framework import (
    TrajectoryStep,
    apply_step,
)

################################################################################
#                               Core class                                      #
################################################################################


class ProteinDiffusionInference:
    """Run reverse‑process inference and turn it into a protein sequence."""

    PROMPT = "Diffusion reverse process:\nInitial state: ''\n"  # ← no “Step 1:”

    def __init__(self, model_path: str, device: str = "cuda") -> None:
        self.device = device
        print(f"Loading model from {model_path} on {device} …")

        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
        self.model.eval()

        if self.tokenizer.pad_token is None:  # make sure padding is defined
            self.tokenizer.pad_token = self.tokenizer.eos_token

        print("Model loaded ✅")

    # ---------------------------------------------------------------------
    # Parsing utilities
    # ---------------------------------------------------------------------

    ACTION_RE = {
        "add": re.compile(r"add\s+(\w)\s+at\s+position\s+(\d+)", re.I),
        "remove": re.compile(r"remove\s+(\w)\s+from\s+position\s+(\d+)", re.I),
        "replace": re.compile(
            r"replace\s+(\w)\s+at\s+position\s+(\d+)\s+with\s+(\w)", re.I
        ),
    }

    CURRENT_STATE_RE = re.compile(r"Current state(?: after step \d+)?:\s*'([^']*)'", re.I)
    STEP_PREFIX_RE = re.compile(r"Step\s+\d+:\s*(.*)")

    # .................................................................

    @staticmethod
    def _clean_line(line: str) -> str:
        """Normalise minor stylistic variations from the model."""
        return line.strip().rstrip(".")  # trim spaces + trailing periods

    def parse_action(self, text: str) -> Optional[TrajectoryStep]:
        text = text.strip()
        if m := self.ACTION_RE["add"].match(text):
            token, pos = m.groups()
            return TrajectoryStep("add", int(pos), token=token)
        if m := self.ACTION_RE["remove"].match(text):
            token, pos = m.groups()
            return TrajectoryStep("remove", int(pos), token=token)
        if m := self.ACTION_RE["replace"].match(text):
            old, pos, new = m.groups()
            return TrajectoryStep("replace", int(pos), token=new, replaced_token=old)
        return None  # line wasn’t an action

    # ---------------------------------------------------------------------
    # Generation + reconstruction
    # ---------------------------------------------------------------------

    @torch.inference_mode()
    def _sample_reverse_text(
        self,
        max_length: int,
        min_length: int,
        temperature: float,
        top_p: float,
    ) -> str:
        inputs = self.tokenizer(self.PROMPT, return_tensors="pt").to(self.device)
        output = self.model.generate(
            **inputs,
            max_length=max_length,
            min_length=min_length,
            temperature=temperature,
            top_p=top_p,
            do_sample=True,
            pad_token_id=self.tokenizer.eos_token_id,
            bad_words_ids=[[self.tokenizer.eos_token_id]],  # never early‑stop
        )
        return self.tokenizer.decode(output[0], skip_special_tokens=True)

    # .................................................................

    def _parse_reverse_text(self, text: str) -> Dict[str, Any]:
        """Replay the text and build a structured trajectory."""
        current = ""  # start from empty string
        steps, states = [], [current]
        step_counter = 0
        cleaned_lines = []  # Store cleaned lines for generated_text

        for raw in text.split("\n"):
            line = self._clean_line(raw)
            if not line:
                continue  # skip blanks

            # Ignore prompt echoes
            if line.lower().startswith(("diffusion reverse process", "initial state")):
                continue

            # Remove optional "Step N:" prefix generated by older checkpoints
            if m := self.STEP_PREFIX_RE.match(line):
                line = m.group(1)

            # Handle explicit state reports (for sanity checks)
            if m := self.CURRENT_STATE_RE.match(line):
                reported = m.group(1)
                if reported != current:
                    print(
                        f"[⚠️] State mismatch: expected '{current}', model said '{reported}'"
                    )
                # Add cleaned state report to output
                cleaned_lines.append(f"Current state: '{reported}'")
                continue  # nothing further to do

            # Otherwise the line *should* be an action description
            action = self.parse_action(line)
            if action is None:
                # The model may hallucinate commentary — skip it.
                continue

            # Enforce constraint: first action must be 'add' when starting from empty string
            if step_counter == 0 and current == "" and action.action != "add":
                print(f"[⚠️] Skipping first non-add action: '{line}' (first action must be 'add')")
                continue

            # Add cleaned action line to output
            cleaned_lines.append(line)

            # Apply edit
            try:
                current = apply_step(current, action)
            except Exception as exc:
                print(f"[❌] Failed to apply '{line}': {exc}")
                break

            step_counter += 1
            states.append(current)
            steps.append({
                "step": step_counter,
                "action": line,
                "state": current,
            })

        # Create cleaned generated text without Step prefixes
        cleaned_text = "Diffusion reverse process:\nInitial state: ''\n" + "\n".join(cleaned_lines)

        return {
            "generated_text": cleaned_text,
            "original_text": text,  # Keep original for debugging if needed
            "final_sequence": current,
            "states": states,
            "steps": steps,
            "num_steps": step_counter,
            "success": step_counter > 0 and len(current) > 0,
        }

    # .................................................................

    def generate_one(
        self,
        *,
        max_length: int = 512,
        min_length: int = 0,
        temperature: float = 0.8,
        top_p: float = 0.9,
    ) -> Dict[str, Any]:
        text = self._sample_reverse_text(max_length, min_length, temperature, top_p)
        return self._parse_reverse_text(text)

    # ------------------------------------------------------------------

    def generate_many(self, n: int, **gen_kwargs) -> List[Dict[str, Any]]:
        print(f"Generating {n} sequences …")
        out = []
        for i in range(n):
            print(f"\n🔹 Sequence {i + 1}/{n}")
            res = self.generate_one(**gen_kwargs)
            if res["success"]:
                print(
                    f"✅ {res['final_sequence']} (len={len(res['final_sequence'])}, steps={res['num_steps']})"
                )
            else:
                print("❌ Generation failed to yield a valid sequence")
            out.append(res)
        return out

################################################################################
#                                   CLI                                        #
################################################################################

def save_results(results: List[Dict[str, Any]], path: str) -> None:
    summary = {
        "total": len(results),
        "successful": sum(r["success"] for r in results),
        "sequences": results,
    }
    with open(path, "w") as fp:
        json.dump(summary, fp, indent=2)
    print(f"Results saved to {path}\n")


def cli():
    p = argparse.ArgumentParser(description="Inference for protein Diffusion‑via‑Reasoning model")
    p.add_argument("--model_path", required=True, help="Directory with model + tokenizer")
    p.add_argument("--num_sequences", type=int, default=5)
    p.add_argument("--max_length", type=int, default=512)
    p.add_argument("--min_length", type=int, default=0)
    p.add_argument("--temperature", type=float, default=0.8)
    p.add_argument("--top_p", type=float, default=0.9)
    p.add_argument("--output", default="inference_results.json")
    p.add_argument("--device", default="cuda", choices=["cuda", "cpu"])
    args = p.parse_args()

    if not os.path.exists(args.model_path):
        raise FileNotFoundError(f"Model directory '{args.model_path}' does not exist.")

    torch.set_default_device(args.device)
    runner = ProteinDiffusionInference(args.model_path, device=args.device)

    results = runner.generate_many(
        args.num_sequences,
        max_length=args.max_length,
        min_length=args.min_length,
        temperature=args.temperature,
        top_p=args.top_p,
    )

    save_results(results, args.output)

    ok = [r for r in results if r["success"]]
    print("\n================= SUMMARY =================")
    print(f"Successful / total : {len(ok)} / {len(results)}  ({100*len(ok)/len(results):.1f}%)")
    if ok:
        avg_len = sum(len(r["final_sequence"]) for r in ok) / len(ok)
        avg_steps = sum(r["num_steps"] for r in ok) / len(ok)
        print(f"Average length     : {avg_len:.1f}")
        print(f"Average #steps     : {avg_steps:.1f}")
        print("Sample sequences   :")
        for s in ok[:3]:
            print("  •", s["final_sequence"])


if __name__ == "__main__":
    cli()
