#!/usr/bin/env python3
"""
Safe inference runner with conservative parameters to avoid hanging.
This script uses proven parameter combinations that work reliably.
"""

import os
import sys
import argparse
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from scripts.inference_runner import Inference<PERSON><PERSON><PERSON>


def run_safe_inference():
    """Run inference with safe, conservative parameters."""
    
    # Default model path
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    print("🛡️  Safe Inference Runner")
    print("=" * 40)
    print("Using conservative parameters to prevent hanging:")
    
    # Conservative parameter sets
    safe_configs = {
        "ultra_safe": {
            "description": "Ultra-safe parameters for testing",
            "num_sequences": 1,
            "max_length": 512,
            "temperature": 0.7,
            "top_p": 0.85,
            "max_steps": 30,
            "print_steps": True
        },
        "safe": {
            "description": "Safe parameters for reliable generation",
            "num_sequences": 2,
            "max_length": 768,
            "temperature": 0.75,
            "top_p": 0.9,
            "max_steps": 50,
            "print_steps": False
        },
        "moderate": {
            "description": "Moderate parameters with good balance",
            "num_sequences": 3,
            "max_length": 1024,
            "temperature": 0.8,
            "top_p": 0.9,
            "max_steps": 100,
            "print_steps": False
        }
    }
    
    print("\nAvailable safe configurations:")
    for name, config in safe_configs.items():
        print(f"  {name}: {config['description']}")
        print(f"    - Sequences: {config['num_sequences']}, Length: {config['max_length']}")
        print(f"    - Temperature: {config['temperature']}, Steps: {config['max_steps']}")
    
    # Ask user which config to use
    print(f"\nWhich configuration would you like to use?")
    print(f"Options: {', '.join(safe_configs.keys())}")
    
    choice = input("Enter choice (or press Enter for 'safe'): ").strip().lower()
    if not choice:
        choice = "safe"
    
    if choice not in safe_configs:
        print(f"Invalid choice. Using 'safe' configuration.")
        choice = "safe"
    
    selected_config = safe_configs[choice]
    print(f"\n🚀 Running with '{choice}' configuration:")
    for key, value in selected_config.items():
        if key != 'description':
            print(f"   {key}: {value}")
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"\n❌ Model path does not exist: {model_path}")
        print("Please update the model_path variable in this script.")
        return
    
    try:
        # Initialize runner
        runner = InferenceRunner(model_path, device="cuda")
        
        # Run inference with safe parameters
        results, output_file = runner.run_inference(
            custom_params=selected_config,
            output_prefix=f"safe_{choice}"
        )
        
        if results and output_file:
            successful = sum(1 for r in results if r["success"])
            print(f"\n🎉 Safe inference completed successfully!")
            print(f"   Generated {successful}/{len(results)} sequences")
            print(f"   Output saved to: {output_file}")
            
            # Show sample results
            if successful > 0:
                successful_results = [r for r in results if r["success"]]
                print(f"\n📄 Sample sequences:")
                for i, result in enumerate(successful_results[:2]):
                    seq = result['final_sequence']
                    print(f"   {i+1}. {seq} (length: {len(seq)})")
        else:
            print(f"\n❌ Safe inference failed")
            
    except Exception as e:
        print(f"\n❌ Error during safe inference: {e}")
        import traceback
        traceback.print_exc()


def main():
    parser = argparse.ArgumentParser(description="Safe inference runner with conservative parameters")
    parser.add_argument("--config", type=str, choices=["ultra_safe", "safe", "moderate"],
                       help="Safe configuration to use")
    parser.add_argument("--model_path", type=str,
                       help="Path to model (overrides default)")
    
    args = parser.parse_args()
    
    if args.config:
        # Non-interactive mode
        model_path = args.model_path or "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
        
        safe_configs = {
            "ultra_safe": {
                "num_sequences": 1,
                "max_length": 512,
                "temperature": 0.7,
                "top_p": 0.85,
                "max_steps": 30,
                "print_steps": True
            },
            "safe": {
                "num_sequences": 2,
                "max_length": 768,
                "temperature": 0.75,
                "top_p": 0.9,
                "max_steps": 50,
                "print_steps": False
            },
            "moderate": {
                "num_sequences": 3,
                "max_length": 1024,
                "temperature": 0.8,
                "top_p": 0.9,
                "max_steps": 100,
                "print_steps": False
            }
        }
        
        selected_config = safe_configs[args.config]
        print(f"🚀 Running {args.config} configuration non-interactively")
        
        if not os.path.exists(model_path):
            print(f"❌ Model path does not exist: {model_path}")
            return
        
        try:
            runner = InferenceRunner(model_path, device="cuda")
            results, output_file = runner.run_inference(
                custom_params=selected_config,
                output_prefix=f"safe_{args.config}"
            )
            
            if results:
                successful = sum(1 for r in results if r["success"])
                print(f"✅ Generated {successful}/{len(results)} sequences")
                print(f"📁 Output: {output_file}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        # Interactive mode
        run_safe_inference()


if __name__ == "__main__":
    main()
