#!/usr/bin/env python3
"""
Simple debug script using only transformers library to test model generation.
"""

import os
import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM

def simple_test():
    """Test model generation with minimal code."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🔍 Simple Debug Test")
    print("=" * 30)
    
    try:
        # Load tokenizer
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print("✅ Tokenizer loaded")
        
        # Load model
        print("Loading model...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        model.eval()
        print("✅ Model loaded")
        
        # Test different prompts
        prompts = [
            "Diffusion reverse process:\nInitial state: ''\nadd",
            "add K at position 0",
            "Diffusion reverse process:\nInitial state: ''\nStep 1: add K at position 0",
            ""
        ]
        
        for i, prompt in enumerate(prompts):
            print(f"\n🧪 Test {i+1}: '{prompt}'")
            
            # Tokenize
            inputs = tokenizer(prompt, return_tensors="pt").to("cuda")
            print(f"   Input tokens: {inputs['input_ids'].shape[1]}")
            
            # Generate with very conservative parameters
            start_time = time.time()
            try:
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=50,  # Very small
                        temperature=0.7,    # Conservative
                        top_p=0.8,         # Conservative
                        do_sample=True,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id,
                        num_return_sequences=1,
                        use_cache=True,
                        repetition_penalty=1.2,  # Higher to prevent loops
                    )
                
                gen_time = time.time() - start_time
                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                print(f"   ✅ Generated in {gen_time:.2f}s")
                print(f"   Length: {len(generated_text)} chars")
                print(f"   Text: '{generated_text}'")
                
                # Check if it contains protein-related content
                if any(word in generated_text.lower() for word in ['add', 'remove', 'replace', 'position']):
                    print(f"   ✅ Contains protein actions")
                else:
                    print(f"   ❌ No protein actions found")
                
            except Exception as e:
                print(f"   ❌ Generation failed: {e}")
        
        print(f"\n🎯 Summary: All tests completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
