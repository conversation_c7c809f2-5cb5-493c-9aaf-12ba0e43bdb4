#!/usr/bin/env python3
"""
Test the improved parsing with actual generated text examples.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from inference_diffusion import ProteinDiffusionInference


def test_parsing():
    """Test parsing with examples from actual generation."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🧪 Testing Improved Parsing")
    print("=" * 40)
    
    try:
        # Initialize inference
        inference = ProteinDiffusionInference(model_path, device="cuda")
        
        # Test parsing with actual examples we saw
        test_lines = [
            "add N at position 0",
            "remove Q from positions 1",
            "replace L in Position 2 With P",
            "add K at position 0",
            "add M to state E IWDLKDGLCMVNQLLRDNLTLHTRSLRSAELIRASAESCWEAEYEGSEIWEDTGLSSSTSGG",
            "Step 1: add K at position 0 with MEGQDGPLVNNYKNVKKWLIKMQQLTYRNSFTSSASPYSHETVSGGSEGVSSGSGEAVSKSGGRESGNKSGAIYYDKPSSYSL",
            "add C at position 1",
            "remove A from position 0",
            "replace M at position 1 with K",
            "Current state: 'MKVC'",
            "with MEGQDGPLVNNYKNVKKWLIKMQQLTYRNSFTSSASPYSHETVSGGSEGVSSGSGEAVSKSGGRESGNKSGAIYYDKPSSYSL"
        ]
        
        print("🔍 Testing parse_action on various lines:")
        for i, line in enumerate(test_lines):
            action = inference.parse_action(line)
            status = "✅" if action else "❌"
            print(f"   {status} Line {i+1}: '{line[:60]}{'...' if len(line) > 60 else ''}' -> {action}")
        
        # Now test with actual generation
        print(f"\n🔄 Testing with actual generation...")
        result = inference.generate_reverse_process(
            max_length=300,  # Small for testing
            temperature=0.8,
            top_p=0.9,
            max_steps=20,
            print_steps=True
        )
        
        print(f"\n📊 Results:")
        print(f"   Success: {result['success']}")
        print(f"   Final sequence: '{result['final_sequence']}'")
        print(f"   Number of steps: {result['num_steps']}")
        
        if result['steps']:
            print(f"\n📋 Parsed steps:")
            for step in result['steps'][:5]:  # Show first 5
                print(f"   Step {step['step']}: {step['action']} -> '{step['state']}'")
        
        print(f"\n📝 Generated text:")
        print("=" * 50)
        print(result['generated_text'])
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_parsing()
