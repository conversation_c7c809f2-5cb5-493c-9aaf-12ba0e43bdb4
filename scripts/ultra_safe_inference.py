#!/usr/bin/env python3
"""
Ultra-safe inference with minimal parameters to avoid hanging.
"""

import os
import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from diffusion_via_reasoning.trajectory.framework import TrajectoryStep, apply_step
import re


class UltraSafeInference:
    """Ultra-safe inference with minimal parameters."""
    
    def __init__(self, model_path, device="cuda"):
        self.model_path = model_path
        self.device = device
        self.load_model()
    
    def load_model(self):
        """Load model and tokenizer."""
        print(f"Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print(f"Loading model...")
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        self.model.eval()
        print(f"✅ Model loaded")
    
    def parse_action(self, action_text: str):
        """Improved action parser to handle various formats."""
        action_text = action_text.strip()

        if not action_text or len(action_text) > 80:  # Slightly more lenient
            return None

        # Skip unwanted patterns but be more specific
        skip_patterns = ['current state', 'diffusion', 'initial']
        if any(pattern in action_text.lower() for pattern in skip_patterns):
            return None

        # Extract just the first part before any long sequences
        # Split on common delimiters that indicate start of protein sequence
        for delimiter in [' with ', ': add', "'", '"', ' (', ' ABCDEFGHIJKLMNOPQRSTUVWXYZ']:
            if delimiter in action_text:
                action_text = action_text.split(delimiter)[0].strip()
                break

        # Standard patterns
        add_match = re.search(r"add\s+([A-Z])\s+(?:at|to)\s+position\s+(\d+)", action_text, re.IGNORECASE)
        if add_match:
            return {
                'action': 'add',
                'token': add_match.group(1).upper(),
                'position': int(add_match.group(2))
            }

        # Handle "add X from Position Y" (treat as add at position Y)
        add_from_match = re.search(r"add\s+([A-Z])\s+from\s+[Pp]osition\s+(\d+)", action_text, re.IGNORECASE)
        if add_from_match:
            return {
                'action': 'add',
                'token': add_from_match.group(1).upper(),
                'position': int(add_from_match.group(2))
            }

        remove_match = re.search(r"remove\s+([A-Z])\s+from\s+positions?\s+(\d+)", action_text, re.IGNORECASE)
        if remove_match:
            return {
                'action': 'remove',
                'token': remove_match.group(1).upper(),
                'position': int(remove_match.group(2))
            }

        replace_match = re.search(r"replace\s+([A-Z])\s+(?:at|in)\s+[Pp]osition\s+(\d+)\s+[Ww]ith\s+([A-Z])", action_text, re.IGNORECASE)
        if replace_match:
            return {
                'action': 'replace',
                'old_token': replace_match.group(1).upper(),
                'position': int(replace_match.group(2)),
                'token': replace_match.group(3).upper()
            }

        # Try to extract simple "add X" at the beginning
        simple_add = re.match(r"add\s+([A-Z])(?:\s|$)", action_text, re.IGNORECASE)
        if simple_add:
            return {
                'action': 'add',
                'token': simple_add.group(1).upper(),
                'position': 0  # Default position
            }

        return None
    
    def apply_action(self, sequence, action):
        """Apply action to sequence."""
        try:
            if action['action'] == 'add':
                pos = min(action['position'], len(sequence))
                return sequence[:pos] + action['token'] + sequence[pos:]
            elif action['action'] == 'remove':
                pos = action['position']
                if 0 <= pos < len(sequence):
                    return sequence[:pos] + sequence[pos+1:]
            elif action['action'] == 'replace':
                pos = action['position']
                if 0 <= pos < len(sequence):
                    return sequence[:pos] + action['token'] + sequence[pos+1:]
        except:
            pass
        return sequence
    
    def generate_sequence(self):
        """Generate a single sequence with ultra-safe parameters."""

        # Best prompt based on testing - more examples help
        prompt = "add K at position 0\nadd C at position 1\nadd V at position 2\nadd R at position 3\nadd"
        
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        
        print(f"🔄 Generating with ultra-safe parameters...")
        print(f"   Input: '{prompt}'")
        print(f"   Input tokens: {inputs['input_ids'].shape[1]}")
        
        start_time = time.time()
        
        try:
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=15,  # Even smaller to force short actions
                    temperature=0.6,    # More conservative
                    top_p=0.7,         # More conservative
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    repetition_penalty=1.5,  # Higher to prevent loops
                    no_repeat_ngram_size=3,  # Prevent longer repetitions
                )
            
            gen_time = time.time() - start_time
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"✅ Generated in {gen_time:.2f}s")
            print(f"📝 Generated text:")
            print(f"'{generated_text}'")
            
            # Parse the generated text
            lines = generated_text.split('\n')
            sequence = ""
            steps = []
            
            print(f"\n🔍 Parsing lines:")
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                action = self.parse_action(line)
                if action:
                    print(f"   ✅ Line {i+1}: '{line}' -> {action}")
                    old_sequence = sequence
                    sequence = self.apply_action(sequence, action)
                    steps.append({
                        'action': line,
                        'parsed': action,
                        'sequence': sequence
                    })
                    print(f"      '{old_sequence}' -> '{sequence}'")
                else:
                    print(f"   ❌ Line {i+1}: '{line}' -> No action")
            
            print(f"\n📊 Results:")
            print(f"   Final sequence: '{sequence}'")
            print(f"   Length: {len(sequence)}")
            print(f"   Steps parsed: {len(steps)}")
            
            return {
                'success': len(steps) > 0,
                'sequence': sequence,
                'steps': steps,
                'generated_text': generated_text
            }
            
        except Exception as e:
            print(f"❌ Generation failed: {e}")
            return {'success': False, 'sequence': '', 'steps': [], 'generated_text': ''}


def main():
    """Run ultra-safe inference test."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🛡️  Ultra-Safe Inference Test")
    print("=" * 40)
    
    try:
        inference = UltraSafeInference(model_path, device="cuda")
        
        # Generate 3 sequences
        for i in range(3):
            print(f"\n🧬 Generating sequence {i+1}/3...")
            result = inference.generate_sequence()
            
            if result['success']:
                print(f"✅ Success! Generated: '{result['sequence']}'")
            else:
                print(f"❌ Failed to generate valid sequence")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
