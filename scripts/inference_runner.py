#!/usr/bin/env python3
"""
Advanced inference runner with configuration support.
Supports multiple inference modes, parameter tuning, and batch processing.
"""

import os
import json
import argparse
import time
from datetime import datetime
from pathlib import Path
import sys

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from inference_diffusion import ProteinDiffusionInference, save_inference_results


class InferenceRunner:
    """Advanced inference runner with configuration management."""
    
    def __init__(self, model_path, device="cuda", config_file=None):
        self.model_path = model_path
        self.device = device
        self.config_file = config_file or "configs/inference_configs.json"
        self.configs = self.load_configs()
        self.inference = None
        
    def load_configs(self):
        """Load inference configurations from JSON file."""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            print(f"⚠️  Config file not found: {self.config_file}")
            return {}
    
    def list_available_configs(self):
        """List all available configuration presets."""
        print("📋 Available inference configurations:")
        for name, config in self.configs.items():
            desc = config.get('description', 'No description')
            print(f"   {name}: {desc}")
            print(f"      - Sequences: {config.get('num_sequences', 'N/A')}")
            print(f"      - Max length: {config.get('max_length', 'N/A')}")
            print(f"      - Temperature: {config.get('temperature', 'N/A')}")
            print(f"      - Max steps: {config.get('max_steps', 'N/A')}")
            print()
    
    def get_config(self, config_name):
        """Get configuration by name."""
        if config_name in self.configs:
            return self.configs[config_name].copy()
        else:
            print(f"❌ Configuration '{config_name}' not found!")
            return None
    
    def initialize_model(self):
        """Initialize the inference model."""
        if self.inference is None:
            print(f"🔄 Loading model from: {self.model_path}")
            try:
                self.inference = ProteinDiffusionInference(self.model_path, self.device)
                print("✅ Model loaded successfully!")
            except Exception as e:
                print(f"❌ Failed to load model: {e}")
                raise
        return self.inference
    
    def run_inference(self, config_name=None, custom_params=None, output_prefix="inference"):
        """Run inference with specified configuration or custom parameters."""
        
        # Get parameters
        if config_name:
            params = self.get_config(config_name)
            if params is None:
                return None, None
            mode_name = config_name
        elif custom_params:
            params = custom_params.copy()
            mode_name = "custom"
        else:
            params = self.get_config("standard")
            mode_name = "standard"
        
        if params is None:
            print("❌ No valid parameters found!")
            return None, None
        
        # Remove description if present
        params.pop('description', None)
        
        print(f"🚀 Running inference with '{mode_name}' configuration:")
        for key, value in params.items():
            print(f"   {key}: {value}")
        
        # Initialize model
        inference = self.initialize_model()
        
        # Run inference
        start_time = time.time()
        try:
            results = inference.generate_multiple_sequences(**params)
            
            # Create output filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{output_prefix}_{mode_name}_{timestamp}.json"
            
            # Save results
            save_inference_results(results, output_file, inference)
            
            inference_time = time.time() - start_time
            print(f"⏱️  Inference completed in {inference_time:.2f} seconds")
            
            return results, output_file
            
        except Exception as e:
            print(f"❌ Inference failed: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    def run_batch_inference(self, config_names, output_prefix="batch"):
        """Run multiple inference configurations in batch."""
        print(f"🔄 Running batch inference with {len(config_names)} configurations...")
        
        all_results = {}
        
        for i, config_name in enumerate(config_names):
            print(f"\n--- Batch {i+1}/{len(config_names)}: {config_name} ---")
            
            results, output_file = self.run_inference(
                config_name=config_name,
                output_prefix=f"{output_prefix}_{i+1:02d}"
            )
            
            if results:
                all_results[config_name] = {
                    'results': results,
                    'output_file': output_file,
                    'successful': sum(1 for r in results if r["success"]),
                    'total': len(results)
                }
        
        # Print batch summary
        print(f"\n📊 BATCH INFERENCE SUMMARY:")
        for config_name, data in all_results.items():
            success_rate = data['successful'] / data['total'] * 100 if data['total'] > 0 else 0
            print(f"   {config_name}: {data['successful']}/{data['total']} ({success_rate:.1f}%) - {data['output_file']}")
        
        return all_results
    
    def compare_configs(self, config_names):
        """Compare different configurations side by side."""
        print("🔍 Configuration Comparison:")
        print(f"{'Parameter':<15}", end="")
        for name in config_names:
            print(f"{name:<15}", end="")
        print()
        print("-" * (15 + 15 * len(config_names)))
        
        # Get all unique parameters
        all_params = set()
        configs = {}
        for name in config_names:
            config = self.get_config(name)
            if config:
                configs[name] = config
                all_params.update(config.keys())
        
        # Remove description from comparison
        all_params.discard('description')
        
        # Print comparison table
        for param in sorted(all_params):
            print(f"{param:<15}", end="")
            for name in config_names:
                value = configs.get(name, {}).get(param, 'N/A')
                print(f"{str(value):<15}", end="")
            print()


def main():
    parser = argparse.ArgumentParser(description="Advanced inference runner with configuration support")
    
    # Model settings
    parser.add_argument("--model_path", type=str,
                       default="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/",
                       help="Path to trained model directory")
    parser.add_argument("--device", type=str, default="cuda", choices=["cuda", "cpu"],
                       help="Device to use for inference")
    parser.add_argument("--config_file", type=str, default="configs/inference_configs.json",
                       help="Path to configuration file")
    
    # Inference modes
    parser.add_argument("--config", type=str,
                       help="Configuration name to use (see --list-configs)")
    parser.add_argument("--batch", nargs="+",
                       help="Run multiple configurations in batch")
    parser.add_argument("--compare", nargs="+",
                       help="Compare multiple configurations")
    
    # Actions
    parser.add_argument("--list-configs", action="store_true",
                       help="List available configurations")
    
    # Custom parameters (override config)
    parser.add_argument("--num_sequences", type=int,
                       help="Number of sequences to generate")
    parser.add_argument("--max_length", type=int,
                       help="Maximum generation length")
    parser.add_argument("--temperature", type=float,
                       help="Sampling temperature")
    parser.add_argument("--top_p", type=float,
                       help="Top-p sampling parameter")
    parser.add_argument("--max_steps", type=int,
                       help="Maximum number of diffusion steps")
    parser.add_argument("--print_steps", action="store_true",
                       help="Print intermediate steps")
    
    # Output settings
    parser.add_argument("--output_prefix", type=str, default="inference",
                       help="Prefix for output filename")
    
    args = parser.parse_args()
    
    # Initialize runner
    runner = InferenceRunner(args.model_path, args.device, args.config_file)
    
    # Handle different actions
    if args.list_configs:
        runner.list_available_configs()
        return
    
    if args.compare:
        runner.compare_configs(args.compare)
        return
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        print(f"❌ Model path does not exist: {args.model_path}")
        return
    
    # Prepare custom parameters if provided
    custom_params = {}
    for param in ['num_sequences', 'max_length', 'temperature', 'top_p', 'max_steps', 'print_steps']:
        value = getattr(args, param)
        if value is not None:
            custom_params[param] = value

    # Add some default conservative parameters if using custom params
    if custom_params and 'max_length' not in custom_params:
        custom_params['max_length'] = 1024  # More conservative default
    if custom_params and 'top_p' not in custom_params:
        custom_params['top_p'] = 0.9
    
    # Run inference
    if args.batch:
        runner.run_batch_inference(args.batch, args.output_prefix)
    elif args.config:
        if custom_params:
            # Override config with custom parameters
            base_config = runner.get_config(args.config)
            if base_config:
                base_config.update(custom_params)
                runner.run_inference(custom_params=base_config, output_prefix=args.output_prefix)
        else:
            runner.run_inference(config_name=args.config, output_prefix=args.output_prefix)
    elif custom_params:
        runner.run_inference(custom_params=custom_params, output_prefix=args.output_prefix)
    else:
        # Default to standard config
        runner.run_inference(config_name="standard", output_prefix=args.output_prefix)


if __name__ == "__main__":
    main()
