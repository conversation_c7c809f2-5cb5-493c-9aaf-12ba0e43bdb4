#!/usr/bin/env python3
"""
Restructure protein training data to include step-by-step states.
The new format will start with empty string state and record each action + resulting state.
"""

import os
import re
import json
import random
import argparse
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# Add the src directory to the path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from diffusion_via_reasoning.trajectory.framework import Trajectory, TrajectoryStep, apply_step


@dataclass
class DirectReverseConfig:
    """Configuration for direct reverse trajectory generation."""
    correct_action_prob: float = 0.7  # Probability of taking correct action (toward target)
    temperature: float = 0.8           # Controls randomness in trajectory length
    random_action_weights: Dict[str, float] = None
    seed: Optional[int] = None
    adaptive_correction: bool = True   # Use adaptive correction probability
    min_correct_prob: float = 0.7     # Minimum correct action probability
    max_correct_prob: float = 0.95    # Maximum correct action probability at target length
    replace_prob: float = 0.5         # Probability of replace operations in random phase
    add_prob: float = 0.4             # Probability of add operations in random phase

    def __post_init__(self):
        if self.random_action_weights is None:
            # For forward: favor additions, minimal deletions
            self.random_action_weights = {"add": 0.6, "delete": 0.2, "replace": 0.2}


class DirectReverseTrajectoryGenerator:
    """Generate trajectories that build from empty to target sequence (flow-matching style)."""

    def __init__(self, valid_tokens: List[str], config: DirectReverseConfig):
        self.valid_tokens = valid_tokens
        self.config = config

        # Initialize random state for this generator instance
        if config.seed is not None:
            self.random_state = random.Random(config.seed)
        else:
            self.random_state = random.Random()

    def generate_deterministic(self, target: str) -> Trajectory:
        """Generate a completely deterministic trajectory that just adds characters in order."""
        steps = []

        # Simple approach: just add each character of the target in order
        for i, char in enumerate(target):
            step = TrajectoryStep(action="add", position=i, token=char)
            steps.append(step)

        metadata = {
            "approach": "deterministic",
            "target_length": len(target),
            "total_steps": len(steps),
            "direction": "empty_to_target",
            "correction_steps": 0
        }

        return Trajectory(initial_sequence="", steps=steps, metadata=metadata)
    
    def _random_char(self, exclude: Optional[str] = None) -> str:
        """Get a random character from natural amino acids only, optionally excluding one."""
        # Use only the 20 standard amino acids (exclude X, Z and other non-standard ones)
        natural_amino_acids = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L',
                              'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']

        # Filter out excluded character and ensure we only use natural amino acids
        choices = [c for c in natural_amino_acids if c != exclude and c in self.valid_tokens]

        # Fallback to all natural amino acids if no valid choices
        if not choices:
            choices = [c for c in natural_amino_acids if c in self.valid_tokens]

        # Final fallback to valid tokens if no natural amino acids available
        if not choices:
            choices = [c for c in self.valid_tokens if c != exclude]

        return self.random_state.choice(choices) if choices else self.random_state.choice(self.valid_tokens)
    
    def _apply_correct_action(self, current: str, target: str) -> tuple[str, TrajectoryStep]:
        """Add the correct next amino acid from target sequence."""
        if len(current) >= len(target):
            return current, None  # already complete
        # Insert the next correct character at end (simple strategy)
        idx = len(current)
        char = target[idx]
        new_current = current + char
        step = TrajectoryStep(action="add", position=idx, token=char)
        return new_current, step
    
    def _apply_random_action(self, current: str, target: str) -> tuple[str, TrajectoryStep]:
        """Apply a random edit action that may diverge from target, but not too drastically."""
        # Limit random actions to be less disruptive when we're close to the target length
        target_len = len(target)
        current_len = len(current)

        # If we're very close to target length, prefer less disruptive actions
        if abs(current_len - target_len) <= 2:
            actions = ["add", "replace"]
            weights = [0.3, 0.7]  # Prefer replace over add when close to target
        else:
            actions = ["add", "delete", "replace"]
            weights = [
                self.config.random_action_weights["add"],
                self.config.random_action_weights["delete"],
                self.config.random_action_weights["replace"]
            ]

        action = self.random_state.choices(actions, weights=weights)[0]

        if action == "add":
            # When adding, prefer positions near the end to be less disruptive
            if len(current) == 0:
                idx = 0
            else:
                # 60% chance to add at end, 40% chance random position (increased randomness)
                if self.random_state.random() < 0.6:
                    idx = len(current)
                else:
                    idx = self.random_state.randint(0, len(current))
            char = self._random_char()
            new_current = current[:idx] + char + current[idx:]
            trajectory_step = TrajectoryStep(action="add", position=idx, token=char)

        elif action == "delete" and len(current) > 0:
            # When deleting, prefer positions near the end to be less disruptive
            if self.random_state.random() < 0.6:
                idx = len(current) - 1
            else:
                idx = self.random_state.randint(0, len(current) - 1)
            char = current[idx]
            new_current = current[:idx] + current[idx + 1:]
            trajectory_step = TrajectoryStep(action="remove", position=idx, token=char)

        elif action == "replace" and len(current) > 0:
            idx = self.random_state.randint(0, len(current) - 1)
            old_char = current[idx]
            new_char = self._random_char(exclude=old_char)
            new_current = current[:idx] + new_char + current[idx + 1:]
            trajectory_step = TrajectoryStep(
                action="replace",
                position=idx,
                token=new_char,
                replaced_token=old_char
            )
        else:
            # Fallback to add if other actions not possible
            idx = len(current)
            char = self._random_char()
            new_current = current[:idx] + char + current[idx:]
            trajectory_step = TrajectoryStep(action="add", position=idx, token=char)

        return new_current, trajectory_step

    def generate(self, target: str) -> Trajectory:
        """Generate a forward trajectory with truly uniform distribution by interleaving corrections."""

        if not target:
            return Trajectory(initial_sequence="", steps=[], metadata={
                "approach": "interleaved_uniform",
                "target_length": 0,
                "total_steps": 0,
                "direction": "empty_to_target",
                "replace_operations": 0
            })

        # NEW APPROACH: Interleave building and correction operations uniformly
        target_length = len(target)
        extra_steps = int(self.config.temperature * target_length)

        # Create a comprehensive operation plan that includes corrections
        all_operations = []

        # Add target building operations
        for i in range(target_length):
            all_operations.append(("build", i, target[i]))

        # Add random operations
        for _ in range(extra_steps):
            rand_val = self.random_state.random()
            if rand_val < self.config.add_prob:
                all_operations.append(("add_random", None, None))
            elif rand_val < self.config.add_prob + self.config.replace_prob:
                all_operations.append(("replace_random", None, None))
            else:
                all_operations.append(("remove_random", None, None))

        # Add correction operations (these will be distributed throughout)
        correction_operations = []
        for i in range(target_length):
            correction_operations.append(("correct_char", i, target[i]))

        # Interleave corrections throughout the process
        total_ops = len(all_operations)
        correction_interval = max(1, total_ops // len(correction_operations))

        final_plan = []
        correction_index = 0

        for i, op in enumerate(all_operations):
            final_plan.append(op)
            # Insert correction operations at regular intervals
            if (i + 1) % correction_interval == 0 and correction_index < len(correction_operations):
                final_plan.append(correction_operations[correction_index])
                correction_index += 1

        # Add any remaining corrections
        while correction_index < len(correction_operations):
            final_plan.append(correction_operations[correction_index])
            correction_index += 1

        # Shuffle the plan for uniform distribution
        self.random_state.shuffle(final_plan)

        # Execute the plan
        forward_steps = []
        current = ""
        target_chars_added = set()  # Track which target characters we've added

        for op_type, target_pos, target_char in final_plan:
            if op_type == "build":
                # Add target character if we haven't already
                if target_pos not in target_chars_added and len(current) < target_length * 1.5:
                    pos = min(len(current), target_pos) if current else 0
                    current = current[:pos] + target_char + current[pos:]
                    forward_steps.append(TrajectoryStep(action="add", position=pos, token=target_char))
                    target_chars_added.add(target_pos)

            elif op_type == "add_random" and len(current) < target_length * 2:
                pos = self.random_state.randint(0, len(current)) if current else 0
                char = self._random_char()
                current = current[:pos] + char + current[pos:]
                forward_steps.append(TrajectoryStep(action="add", position=pos, token=char))

            elif op_type == "remove_random" and len(current) > 0:
                pos = self.random_state.randint(0, len(current) - 1)
                char = current[pos]
                current = current[:pos] + current[pos + 1:]
                forward_steps.append(TrajectoryStep(action="remove", position=pos, token=char))

            elif op_type == "replace_random" and len(current) > 0:
                pos = self.random_state.randint(0, len(current) - 1)
                old_char = current[pos]
                new_char = self._random_char(exclude=old_char)
                current = current[:pos] + new_char + current[pos + 1:]
                forward_steps.append(TrajectoryStep(action="replace", position=pos, token=new_char, replaced_token=old_char))

            elif op_type == "correct_char":
                # Gentle correction: only fix if we're close to target length and character is wrong
                if len(current) >= target_pos and len(current) <= target_length + 5:
                    if target_pos < len(current) and current[target_pos] != target_char:
                        # Use replace to correct
                        old_char = current[target_pos]
                        current = current[:target_pos] + target_char + current[target_pos + 1:]
                        forward_steps.append(TrajectoryStep(action="replace", position=target_pos, token=target_char, replaced_token=old_char))
                    elif target_pos >= len(current):
                        # Use add to extend
                        current = current + target_char
                        forward_steps.append(TrajectoryStep(action="add", position=len(current) - 1, token=target_char))

        # Final minimal correction phase (only if really necessary)
        correction_steps_start = len(forward_steps)

        # Only do minimal corrections
        while len(current) > len(target):
            pos = len(current) - 1
            char = current[pos]
            current = current[:pos]
            forward_steps.append(TrajectoryStep(action="remove", position=pos, token=char))

        while len(current) < len(target):
            pos = len(current)
            char = target[pos]
            current = current + char
            forward_steps.append(TrajectoryStep(action="add", position=pos, token=char))

        # Only fix critical mismatches
        for i, target_char in enumerate(target):
            if i < len(current) and current[i] != target_char:
                old_char = current[i]
                current = current[:i] + target_char + current[i + 1:]
                forward_steps.append(TrajectoryStep(action="replace", position=i, token=target_char, replaced_token=old_char))

        # Verify trajectory
        test_current = ""
        for step in forward_steps:
            test_current = apply_step(test_current, step)

        if test_current != target:
            # Minimal fallback
            forward_steps = []
            for i, char in enumerate(target):
                forward_steps.append(TrajectoryStep(action="add", position=i, token=char))

        # Analyze distribution
        action_counts = {"add": 0, "remove": 0, "replace": 0}
        for step in forward_steps:
            action_counts[step.action] += 1

        final_correction_count = len(forward_steps) - correction_steps_start

        metadata = {
            "approach": "interleaved_uniform",
            "config": {
                "temperature": self.config.temperature,
                "add_prob": self.config.add_prob,
                "replace_prob": self.config.replace_prob,
            },
            "target_length": len(target),
            "total_steps": len(forward_steps),
            "direction": "empty_to_target",
            "action_distribution": action_counts,
            "replace_operations": action_counts["replace"],
            "final_correction_steps": final_correction_count,
            "verification_passed": test_current == target
        }

        return Trajectory(initial_sequence="", steps=forward_steps, metadata=metadata)


def trajectory_to_states(trajectory: Trajectory) -> List[str]:
    """Convert trajectory to list of intermediate states."""
    states = [trajectory.initial_sequence]
    current = trajectory.initial_sequence
    
    for step in trajectory.steps:
        current = apply_step(current, step)
        states.append(current)
    
    return states


def format_action_step(step: TrajectoryStep) -> str:
    """Format a trajectory step as a readable action string."""
    if step.action == "remove":
        return f"remove {step.token} from position {step.position}"
    elif step.action == "add":
        return f"add {step.token} at position {step.position}"
    elif step.action == "replace":
        return f"replace {step.replaced_token} at position {step.position} with {step.token}"
    else:
        return f"{step.action} {step.token} at position {step.position}"


def create_restructured_training_example(sequence: str, header: str, generator) -> Dict[str, any]:
    """Create a restructured training example with simplified format and regular state updates."""
    # Generate trajectory from empty string to target sequence
    trajectory = generator.generate(sequence)

    # Get all intermediate states
    states = trajectory_to_states(trajectory)

    # VERIFICATION: Check that final state matches target
    final_state = states[-1]
    if final_state != sequence:
        print(f"WARNING: Final state '{final_state}' does not match target '{sequence}'")
        print(f"  Target length: {len(sequence)}, Final length: {len(final_state)}")
        # This should not happen with the correction logic, but let's check

    # Create simplified reverse process list with state tracking
    reverse_process = []

    # Start with empty string
    reverse_process.append("")

    # Add each action description with regular state updates
    for i, step in enumerate(trajectory.steps):
        action_str = format_action_step(step)
        reverse_process.append(action_str)

        # Every 10 steps or at the final step, add the current state
        if (i + 1) % 10 == 0 or i == len(trajectory.steps) - 1:
            current_state = states[i + 1]  # +1 because states includes initial state
            reverse_process.append(f"Current state: '{current_state}'")

        # Also add intermediate checks for very short sequences (< 20 steps)
        elif len(trajectory.steps) <= 20 and (i + 1) % 5 == 0:
            current_state = states[i + 1]
            reverse_process.append(f"Current state: '{current_state}'")

    # Always add the final state at the end if not already added
    final_state = states[-1]
    if not reverse_process[-1].startswith("Current state"):
        reverse_process.append(f"Final state: '{final_state}'")

    return {
        "reverse_process": reverse_process
    }


def read_fasta_sequences(fasta_path: str) -> List[Tuple[str, str]]:
    """Read protein sequences from a FASTA file."""
    sequences = []
    current_header = None
    current_seq = ""
    
    with open(fasta_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                if current_header is not None and current_seq:
                    sequences.append((current_header, current_seq))
                current_header = line[1:]  # Remove '>'
                current_seq = ""
            else:
                current_seq += line
        
        # Don't forget the last sequence
        if current_header is not None and current_seq:
            sequences.append((current_header, current_seq))
    
    return sequences


def extract_protein_charset(sequences: List[Tuple[str, str]]) -> List[str]:
    """Extract all unique amino acid characters from protein sequences."""
    charset = set()
    for _, seq in sequences:
        charset.update(seq)
    return sorted(list(charset))


def split_sequences_randomly(sequences: List[Tuple[str, str]], 
                           train_ratio: float = 0.7, 
                           test_ratio: float = 0.2, 
                           val_ratio: float = 0.1,
                           seed: int = 42) -> Dict[str, List[Tuple[str, str]]]:
    """Randomly split sequences into train, test, and optionally validation sets."""
    random.seed(seed)
    shuffled = sequences.copy()
    random.shuffle(shuffled)
    
    total = len(shuffled)
    train_end = int(total * train_ratio)
    test_end = train_end + int(total * test_ratio)
    val_end = test_end + int(total * val_ratio)

    splits = {
        'train': shuffled[:train_end],
        'test': shuffled[train_end:test_end],
    }

    # Add validation set if specified
    if val_ratio > 0:
        splits['validation'] = shuffled[test_end:val_end]
    
    return splits


def generate_restructured_examples_for_sequences(sequences: List[Tuple[str, str]],
                                               generator_config,
                                               charset: List[str],
                                               split_name: str,
                                               base_seed: int = 42,
                                               num_seeds: int = 1) -> List[Dict[str, any]]:
    """Generate restructured training examples for a list of sequences with multiple seeds."""
    examples = []

    print(f"Generating restructured training examples for {len(sequences)} sequences in {split_name} set with {num_seeds} seeds...")

    for seed_idx in range(num_seeds):
        current_seed = base_seed + seed_idx * 10000  # Ensure seeds are well separated
        print(f"  Using seed {current_seed} ({seed_idx + 1}/{num_seeds})")

        for i, (header, seq) in enumerate(sequences):
            if i % 50 == 0:
                print(f"    Processing sequence {i+1}/{len(sequences)} with seed {current_seed}")

            try:
                # Create a new generator with a unique seed for each sequence and seed combination
                sequence_seed = current_seed + i if current_seed is not None else None
                config = DirectReverseConfig(
                    correct_action_prob=generator_config.correct_action_prob,
                    temperature=generator_config.temperature,
                    random_action_weights=generator_config.random_action_weights,
                    adaptive_correction=generator_config.adaptive_correction,
                    min_correct_prob=generator_config.min_correct_prob,
                    max_correct_prob=generator_config.max_correct_prob,
                    replace_prob=generator_config.replace_prob,
                    add_prob=generator_config.add_prob,
                    seed=sequence_seed
                )
                generator = DirectReverseTrajectoryGenerator(charset, config)

                example = create_restructured_training_example(seq, header, generator)
                examples.append(example)
            except Exception as e:
                print(f"    Error processing sequence {i+1} with seed {current_seed}: {e}")
                continue

    print(f"Generated {len(examples)} restructured training examples from {split_name} set with {num_seeds} seeds")
    return examples


def save_restructured_training_data(train_examples: List[Dict[str, any]],
                                  test_examples: List[Dict[str, any]],
                                  val_examples: Optional[List[Dict[str, any]]] = None,
                                  output_dir: str = "./data/CreateProtein_restructured"):
    """Save restructured training data to separate JSON files for train/test/val."""
    os.makedirs(output_dir, exist_ok=True)

    # Always save as separate files for train/test/val
    # Save train data
    train_path = os.path.join(output_dir, "train_data_restructured.json")
    with open(train_path, 'w') as f:
        json.dump(train_examples, f, indent=2)
    print(f"Saved {len(train_examples)} restructured training examples to {train_path}")

    # Save test data
    test_path = os.path.join(output_dir, "test_data_restructured.json")
    with open(test_path, 'w') as f:
        json.dump(test_examples, f, indent=2)
    print(f"Saved {len(test_examples)} restructured test examples to {test_path}")

    # Save validation data if provided
    if val_examples:
        val_path = os.path.join(output_dir, "val_data_restructured.json")
        with open(val_path, 'w') as f:
            json.dump(val_examples, f, indent=2)
        print(f"Saved {len(val_examples)} restructured validation examples to {val_path}")

    # Save summary statistics
    stats = {
        "train_count": len(train_examples),
        "test_count": len(test_examples),
        "val_count": len(val_examples) if val_examples else 0,
        "total_count": len(train_examples) + len(test_examples) + (len(val_examples) if val_examples else 0),
        "created_at": datetime.now().isoformat(),
        "data_format": "simplified_reverse_process"
    }

    if train_examples:
        # Calculate some statistics from training examples
        train_steps = [len(ex["reverse_process"]) - 1 for ex in train_examples]  # -1 because first element is empty string

        stats.update({
            "avg_steps": sum(train_steps) / len(train_steps),
            "min_steps": min(train_steps),
            "max_steps": max(train_steps)
        })

    stats_path = os.path.join(output_dir, "data_stats_restructured.json")
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    print(f"Saved restructured data statistics to {stats_path}")


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Create restructured protein diffusion training data")
    parser.add_argument("--fasta", default="./data/uniprot_sprot_head1000.fasta", 
                       help="Path to FASTA file")
    parser.add_argument("--output-dir", default="./data/CreateProtein_restructured_head1000_new", 
                       help="Output directory for restructured training data")
    parser.add_argument("--train-ratio", type=float, default=0.7, help="Training set ratio")
    parser.add_argument("--test-ratio", type=float, default=0.1, help="Test set ratio")
    parser.add_argument("--val-ratio", type=float, default=0.2, help="Validation set ratio (0 to disable)")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    parser.add_argument("--correct-action-prob", type=float, default=0.6,
                       help="Probability of taking correct action toward target")
    parser.add_argument("--temperature", type=float, default=1.2,
                       help="Temperature for trajectory length variation")
    parser.add_argument("--add-weight", type=float, default=0.4,
                       help="Weight for add actions in random operations")
    parser.add_argument("--delete-weight", type=float, default=0.3,
                       help="Weight for delete actions in random operations")
    parser.add_argument("--replace-weight", type=float, default=0.3,
                       help="Weight for replace actions in random operations")
    parser.add_argument("--adaptive-correction", action="store_true", default=True,
                       help="Use adaptive correction probability (increases as sequence approaches target length)")
    parser.add_argument("--min-correct-prob", type=float, default=0.6,
                       help="Minimum correct action probability at start of sequence")
    parser.add_argument("--max-correct-prob", type=float, default=0.95,
                       help="Maximum correct action probability near target length")
    parser.add_argument("--replace-prob", type=float, default=0.5,
                       help="Probability of replace operations in random phase (0.0-1.0)")
    parser.add_argument("--add-prob", type=float, default=0.4,
                       help="Probability of add operations in random phase (0.0-1.0)")
    parser.add_argument("--num-seeds", type=int, default=1,
                       help="Number of different seeds to use for data generation")
    return parser.parse_args()


def main():
    """Main function to create restructured training data."""
    args = parse_args()
    
    print(f"Reading sequences from {args.fasta}...")
    if not os.path.exists(args.fasta):
        print(f"Error: FASTA file {args.fasta} not found!")
        return
    
    sequences = read_fasta_sequences(args.fasta)
    print(f"Read {len(sequences)} sequences")

    if len(sequences) == 0:
        print("No sequences found in FASTA file!")
        return

    # Filter sequences by length BEFORE splitting
    print("Filtering sequences by length...")
    filtered_sequences = []
    for header, seq in sequences:
        if 10 <= len(seq) <= 300:
            filtered_sequences.append((header, seq))
        else:
            # Optionally print skipped sequences for debugging
            pass

    print(f"Filtered to {len(filtered_sequences)} sequences (length 10-300)")

    if len(filtered_sequences) == 0:
        print("No sequences found in valid length range (10-300)!")
        return

    # Extract character set for trajectory generation
    charset = extract_protein_charset(filtered_sequences)
    print(f"Extracted charset: {charset}")

    # Split filtered sequences
    print("Splitting sequences...")
    splits = split_sequences_randomly(
        filtered_sequences,
        train_ratio=args.train_ratio,
        test_ratio=args.test_ratio,
        val_ratio=args.val_ratio,
        seed=args.seed
    )
    
    for split_name, split_seqs in splits.items():
        print(f"  {split_name}: {len(split_seqs)} sequences")
    
    # Create trajectory generator configuration
    generator_config = DirectReverseConfig(
        correct_action_prob=args.correct_action_prob,
        temperature=args.temperature,
        random_action_weights={
            "add": args.add_weight,
            "delete": args.delete_weight,
            "replace": args.replace_weight
        },
        adaptive_correction=args.adaptive_correction,
        min_correct_prob=args.min_correct_prob,
        max_correct_prob=args.max_correct_prob,
        replace_prob=args.replace_prob,
        add_prob=args.add_prob,
        seed=None  # Will be set per sequence
    )
    print("Using DirectReverseTrajectoryGenerator with adaptive correction")
    print(f"Number of seeds: {args.num_seeds}")
    print(f"Base seed: {args.seed}")
    print(f"Action weights: add={generator_config.random_action_weights['add']}, delete={generator_config.random_action_weights['delete']}, replace={generator_config.random_action_weights['replace']}")
    print(f"Adaptive correction: {generator_config.adaptive_correction}")
    if generator_config.adaptive_correction:
        print(f"Correct action probability range: {generator_config.min_correct_prob} -> {generator_config.max_correct_prob}")
    else:
        print(f"Fixed correct action probability: {generator_config.correct_action_prob}")
    print(f"Temperature: {generator_config.temperature}")

    # Generate restructured training examples for each split with multiple seeds
    print(f"\nGenerating data with {args.num_seeds} seeds...")

    # Collect all examples across all seeds
    all_train_examples = []
    all_test_examples = []
    all_val_examples = []

    for seed_idx in range(args.num_seeds):
        current_seed = args.seed + seed_idx * 10000
        print(f"\nProcessing seed {seed_idx + 1}/{args.num_seeds} (seed={current_seed})")

        # Generate examples for each split with current seed
        seed_examples = {}
        for split_name, split_seqs in splits.items():
            examples = generate_restructured_examples_for_sequences(
                split_seqs, generator_config, charset, split_name, current_seed, 1  # Use 1 seed per call
            )
            seed_examples[split_name] = examples

        # Accumulate examples from this seed
        all_train_examples.extend(seed_examples["train"])
        all_test_examples.extend(seed_examples["test"])
        if "validation" in seed_examples:
            all_val_examples.extend(seed_examples["validation"])

    print(f"\nTotal examples generated:")
    print(f"  Train: {len(all_train_examples)} examples")
    print(f"  Test: {len(all_test_examples)} examples")
    if all_val_examples:
        print(f"  Validation: {len(all_val_examples)} examples")

    # Save restructured training data
    print(f"\nSaving restructured training data to {args.output_dir}...")
    save_restructured_training_data(
        all_train_examples,
        all_test_examples,
        all_val_examples if all_val_examples else None,
        args.output_dir
    )
    
    # Print sample restructured data format
    print("\n" + "="*80)
    print("SAMPLE RESTRUCTURED TRAINING EXAMPLE")
    print("="*80)
    if all_examples["train"]:
        example = all_examples["train"][0]
        print("SIMPLIFIED FORMAT:")
        print(f"reverse_process: {example['reverse_process'][:6]}...")  # Show first 6 elements
        print(f"\nTotal elements: {len(example['reverse_process'])}")
        print(f"First element (empty string): '{example['reverse_process'][0]}'")
        print(f"Second element (first action): '{example['reverse_process'][1]}'")
        if len(example['reverse_process']) > 2:
            print(f"Last element (final action): '{example['reverse_process'][-1]}'")
    
    print(f"\nRestructured training data creation completed! Data saved to {args.output_dir}")


if __name__ == "__main__":
    main() 