#!/usr/bin/env python3
"""
Debug script to see what the model is actually generating and why parsing fails.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from inference_diffusion import ProteinDiffusionInference


def debug_generation():
    """Debug what the model is generating."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🔍 Debug Generation Script")
    print("=" * 40)
    
    try:
        # Initialize inference
        print("Loading model...")
        inference = ProteinDiffusionInference(model_path, device="cuda")
        print("✅ Model loaded!")
        
        # Generate with debug enabled
        print("\n🔄 Generating with debug output...")
        result = inference.generate_reverse_process(
            max_length=512,  # Small for debugging
            temperature=0.8,
            top_p=0.9,
            max_steps=50,
            print_steps=True  # Enable debug output
        )
        
        print(f"\n📊 Generation Results:")
        print(f"   Success: {result['success']}")
        print(f"   Final sequence: '{result['final_sequence']}'")
        print(f"   Sequence length: {len(result['final_sequence'])}")
        print(f"   Number of steps: {result['num_steps']}")
        
        print(f"\n📝 Full Generated Text:")
        print("=" * 60)
        print(result['generated_text'])
        print("=" * 60)
        
        print(f"\n🔍 Text Analysis:")
        lines = result['generated_text'].split('\n')
        print(f"   Total lines: {len(lines)}")
        
        for i, line in enumerate(lines[:20]):  # Show first 20 lines
            line = line.strip()
            if line:
                print(f"   Line {i+1}: '{line}'")
        
        if len(lines) > 20:
            print(f"   ... and {len(lines) - 20} more lines")
        
        # Check if it contains expected patterns
        text = result['generated_text'].lower()
        patterns = ['add', 'remove', 'replace', 'position', 'current state']
        
        print(f"\n🔍 Pattern Analysis:")
        for pattern in patterns:
            count = text.count(pattern)
            print(f"   '{pattern}': {count} occurrences")
        
        # Show parsed steps
        if result['steps']:
            print(f"\n📋 Parsed Steps:")
            for i, step in enumerate(result['steps'][:10]):  # Show first 10 steps
                print(f"   Step {step['step']}: {step['action']} -> '{step['state']}'")
            if len(result['steps']) > 10:
                print(f"   ... and {len(result['steps']) - 10} more steps")
        else:
            print(f"\n❌ No steps were parsed from the generated text!")
            print(f"\n🔧 Let's try to understand why...")
            
            # Check what the parse_action method sees
            print(f"\n🔍 Testing parse_action on each line:")
            for i, line in enumerate(lines[:10]):
                line = line.strip()
                if line and not line.startswith("Diffusion") and not line.startswith("Initial"):
                    action = inference.parse_action(line)
                    print(f"   Line {i+1}: '{line}' -> {action}")
            
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_generation()
