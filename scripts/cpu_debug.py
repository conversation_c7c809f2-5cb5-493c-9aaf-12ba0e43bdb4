#!/usr/bin/env python3
"""
CPU-only debug script to test if the issue is GPU-related.
"""

import os
import torch
import time
from transformers import AutoTokenizer, AutoModelForCausalLM

def cpu_test():
    """Test model generation on CPU."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🖥️  CPU Debug Test")
    print("=" * 30)
    
    try:
        # Load tokenizer
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print("✅ Tokenizer loaded")
        
        # Load model on CPU
        print("Loading model on CPU...")
        model = AutoModelForCausalLM.from_pretrained(model_path)
        model = model.to("cpu")
        model.eval()
        print("✅ Model loaded on CPU")
        
        # Simple test
        prompt = "Diffusion reverse process:\nInitial state: ''\nadd"
        print(f"\n🧪 Testing prompt: '{prompt}'")
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt")
        print(f"   Input tokens: {inputs['input_ids'].shape[1]}")
        
        # Generate with minimal parameters
        print("   Generating...")
        start_time = time.time()
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=30,  # Very small
                temperature=0.8,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        
        gen_time = time.time() - start_time
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        print(f"   ✅ Generated in {gen_time:.2f}s")
        print(f"   Text: '{generated_text}'")
        
        # Analyze the output
        lines = generated_text.split('\n')
        print(f"\n📝 Analysis:")
        print(f"   Total lines: {len(lines)}")
        for i, line in enumerate(lines):
            if line.strip():
                print(f"   Line {i+1}: '{line.strip()}'")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    cpu_test()
