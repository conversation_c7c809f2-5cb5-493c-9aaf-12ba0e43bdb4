#!/usr/bin/env python3
"""
Test script for the new inference runners.
This script tests the functionality without requiring a full model run.
"""

import os
import sys
import json
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

def test_config_loading():
    """Test configuration file loading."""
    print("🧪 Testing configuration loading...")
    
    config_file = "configs/inference_configs.json"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            configs = json.load(f)
        
        print(f"✅ Loaded {len(configs)} configurations:")
        for name, config in configs.items():
            desc = config.get('description', 'No description')
            print(f"   - {name}: {desc}")
        
        # Test specific config structure
        if 'quick_test' in configs:
            quick_config = configs['quick_test']
            required_params = ['num_sequences', 'max_length', 'temperature', 'top_p', 'max_steps']
            missing_params = [p for p in required_params if p not in quick_config]
            
            if not missing_params:
                print("✅ Configuration structure is valid")
            else:
                print(f"❌ Missing parameters in quick_test: {missing_params}")
        
        return True
    else:
        print(f"❌ Configuration file not found: {config_file}")
        return False

def test_inference_runner_import():
    """Test importing the inference runner."""
    print("\n🧪 Testing inference runner import...")
    
    try:
        from scripts.inference_runner import InferenceRunner
        print("✅ Successfully imported InferenceRunner")
        
        # Test initialization without model
        runner = InferenceRunner("dummy_path", "cpu", "configs/inference_configs.json")
        print("✅ Successfully created InferenceRunner instance")
        
        # Test config loading
        configs = runner.configs
        if configs:
            print(f"✅ Loaded {len(configs)} configurations")
        else:
            print("⚠️  No configurations loaded")
        
        # Test config listing
        print("\n📋 Available configurations:")
        runner.list_available_configs()
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to import or initialize InferenceRunner: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimized_runner_import():
    """Test importing the optimized runner."""
    print("\n🧪 Testing optimized runner import...")
    
    try:
        # Test if the functions can be imported
        sys.path.append('scripts')
        import run_inference_optimized
        
        print("✅ Successfully imported run_inference_optimized")
        
        # Test function availability
        functions = ['run_quick_test', 'run_standard_inference', 'run_high_quality_inference', 'run_custom_inference']
        for func_name in functions:
            if hasattr(run_inference_optimized, func_name):
                print(f"✅ Function {func_name} available")
            else:
                print(f"❌ Function {func_name} not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to import run_inference_optimized: {e}")
        return False

def test_file_structure():
    """Test if all required files exist."""
    print("\n🧪 Testing file structure...")
    
    required_files = [
        "scripts/inference_diffusion.py",
        "scripts/inference_runner.py", 
        "scripts/run_inference_optimized.py",
        "scripts/inference_examples.sh",
        "configs/inference_configs.json",
        "INFERENCE_GUIDE.md"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - NOT FOUND")
            all_exist = False
    
    return all_exist

def test_config_comparison():
    """Test configuration comparison functionality."""
    print("\n🧪 Testing configuration comparison...")
    
    try:
        from scripts.inference_runner import InferenceRunner
        runner = InferenceRunner("dummy_path", "cpu")
        
        # Test comparison
        config_names = ['quick_test', 'standard', 'high_quality']
        available_configs = [name for name in config_names if name in runner.configs]
        
        if len(available_configs) >= 2:
            print(f"Testing comparison with: {available_configs}")
            runner.compare_configs(available_configs)
            print("✅ Configuration comparison works")
            return True
        else:
            print("⚠️  Not enough configurations for comparison test")
            return False
            
    except Exception as e:
        print(f"❌ Configuration comparison failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing New Inference System")
    print("=" * 40)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Configuration Loading", test_config_loading),
        ("Inference Runner Import", test_inference_runner_import),
        ("Optimized Runner Import", test_optimized_runner_import),
        ("Configuration Comparison", test_config_comparison),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The inference system is ready to use.")
        print("\n💡 Next steps:")
        print("   1. Update model path in scripts or use --model_path")
        print("   2. Try: python scripts/inference_runner.py --list-configs")
        print("   3. Run: python scripts/inference_runner.py --config quick_test")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
