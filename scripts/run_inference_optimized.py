#!/usr/bin/env python3
"""
Optimized inference runner for protein diffusion model.
This script provides easy parameter tuning and multiple inference modes.
"""

import os
import json
import argparse
import time
from datetime import datetime
from pathlib import Path
import sys

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from scripts.inference_diffusion import ProteinDiffusionInference, save_inference_results


def get_default_model_path():
    """Get the default model path."""
    return "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"


def create_output_filename(prefix="inference", mode="standard"):
    """Create a timestamped output filename."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{mode}_{timestamp}.json"


def run_quick_test(model_path, device="cuda"):
    """Run a quick test with minimal parameters."""
    print("🚀 Running QUICK TEST mode...")
    print("   - Small parameters for fast testing")
    print("   - 1 sequence, max 50 steps")
    
    try:
        inference = ProteinDiffusionInference(model_path, device)
        
        results = inference.generate_multiple_sequences(
            num_sequences=1,
            max_length=512,
            temperature=0.8,
            top_p=0.9,
            max_steps=50,
            print_steps=True
        )
        
        output_file = create_output_filename("quick_test", "fast")
        save_inference_results(results, output_file, inference)
        
        return results, output_file
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return None, None


def run_standard_inference(model_path, device="cuda", num_sequences=3):
    """Run standard inference with balanced parameters."""
    print("🧬 Running STANDARD INFERENCE mode...")
    print(f"   - {num_sequences} sequences")
    print("   - Balanced parameters for quality vs speed")
    
    try:
        inference = ProteinDiffusionInference(model_path, device)
        
        results = inference.generate_multiple_sequences(
            num_sequences=num_sequences,
            max_length=1024,
            temperature=0.8,
            top_p=0.9,
            max_steps=150,
            print_steps=False
        )
        
        output_file = create_output_filename("standard", "balanced")
        save_inference_results(results, output_file, inference)
        
        return results, output_file
        
    except Exception as e:
        print(f"❌ Standard inference failed: {e}")
        return None, None


def run_high_quality_inference(model_path, device="cuda", num_sequences=2):
    """Run high-quality inference with optimized parameters for longer sequences."""
    print("⭐ Running HIGH QUALITY mode...")
    print(f"   - {num_sequences} sequences")
    print("   - Optimized for longer, higher quality protein sequences")
    
    try:
        inference = ProteinDiffusionInference(model_path, device)
        
        results = inference.generate_multiple_sequences(
            num_sequences=num_sequences,
            max_length=2048,
            temperature=0.7,  # Lower temperature for more consistency
            top_p=0.95,       # Higher top_p for better quality
            max_steps=300,    # More steps for longer sequences
            print_steps=True
        )
        
        output_file = create_output_filename("high_quality", "optimized")
        save_inference_results(results, output_file, inference)
        
        return results, output_file
        
    except Exception as e:
        print(f"❌ High quality inference failed: {e}")
        return None, None


def run_custom_inference(model_path, device="cuda", **kwargs):
    """Run inference with custom parameters."""
    print("🔧 Running CUSTOM INFERENCE mode...")
    print("   - Using provided custom parameters")
    
    # Default parameters
    params = {
        'num_sequences': 3,
        'max_length': 1024,
        'temperature': 0.8,
        'top_p': 0.9,
        'max_steps': 200,
        'print_steps': False
    }
    
    # Update with custom parameters
    params.update(kwargs)
    
    print(f"   - Parameters: {params}")
    
    try:
        inference = ProteinDiffusionInference(model_path, device)
        
        results = inference.generate_multiple_sequences(**params)
        
        output_file = create_output_filename("custom", "user_defined")
        save_inference_results(results, output_file, inference)
        
        return results, output_file
        
    except Exception as e:
        print(f"❌ Custom inference failed: {e}")
        return None, None


def print_results_summary(results, output_file):
    """Print a summary of the inference results."""
    if not results:
        return
    
    successful = sum(1 for r in results if r["success"])
    
    print(f"\n📊 INFERENCE SUMMARY:")
    print(f"   Output file: {output_file}")
    print(f"   Total attempts: {len(results)}")
    print(f"   Successful: {successful}")
    print(f"   Success rate: {successful/len(results)*100:.1f}%")
    
    if successful > 0:
        successful_results = [r for r in results if r["success"]]
        lengths = [len(r["final_sequence"]) for r in successful_results]
        steps = [r["num_steps"] for r in successful_results]
        
        print(f"   Average sequence length: {sum(lengths)/len(lengths):.1f}")
        print(f"   Average steps: {sum(steps)/len(steps):.1f}")
        print(f"   Length range: {min(lengths)} - {max(lengths)}")
        
        print(f"\n🧬 Sample sequences:")
        for i, result in enumerate(successful_results[:2]):
            seq = result['final_sequence']
            if len(seq) > 60:
                display_seq = f"{seq[:30]}...{seq[-30:]}"
            else:
                display_seq = seq
            print(f"   {i+1}. {display_seq} (len={len(seq)})")


def main():
    parser = argparse.ArgumentParser(description="Optimized inference runner for protein diffusion")
    
    # Model and device settings
    parser.add_argument("--model_path", type=str, default=get_default_model_path(),
                       help="Path to trained model directory")
    parser.add_argument("--device", type=str, default="cuda", choices=["cuda", "cpu"],
                       help="Device to use for inference")
    
    # Inference modes
    parser.add_argument("--mode", type=str, default="standard",
                       choices=["quick", "standard", "high_quality", "custom"],
                       help="Inference mode with predefined parameter sets")
    
    # Custom parameters (used when mode=custom)
    parser.add_argument("--num_sequences", type=int, default=3,
                       help="Number of sequences to generate")
    parser.add_argument("--max_length", type=int, default=1024,
                       help="Maximum generation length")
    parser.add_argument("--temperature", type=float, default=0.8,
                       help="Sampling temperature")
    parser.add_argument("--top_p", type=float, default=0.9,
                       help="Top-p sampling parameter")
    parser.add_argument("--max_steps", type=int, default=200,
                       help="Maximum number of diffusion steps")
    parser.add_argument("--print_steps", action="store_true",
                       help="Print intermediate steps during generation")
    
    # Output settings
    parser.add_argument("--output_prefix", type=str, default="inference",
                       help="Prefix for output filename")
    
    args = parser.parse_args()
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        print(f"❌ Model path does not exist: {args.model_path}")
        print("Please check the model path or train a model first.")
        return
    
    print(f"🚀 Protein Diffusion Inference Runner")
    print(f"   Model: {args.model_path}")
    print(f"   Device: {args.device}")
    print(f"   Mode: {args.mode}")
    
    start_time = time.time()
    
    # Run inference based on mode
    if args.mode == "quick":
        results, output_file = run_quick_test(args.model_path, args.device)
    elif args.mode == "standard":
        results, output_file = run_standard_inference(args.model_path, args.device, args.num_sequences)
    elif args.mode == "high_quality":
        results, output_file = run_high_quality_inference(args.model_path, args.device, args.num_sequences)
    elif args.mode == "custom":
        custom_params = {
            'num_sequences': args.num_sequences,
            'max_length': args.max_length,
            'temperature': args.temperature,
            'top_p': args.top_p,
            'max_steps': args.max_steps,
            'print_steps': args.print_steps
        }
        results, output_file = run_custom_inference(args.model_path, args.device, **custom_params)
    
    total_time = time.time() - start_time
    
    if results and output_file:
        print_results_summary(results, output_file)
        print(f"\n⏱️  Total time: {total_time:.2f} seconds")
        print(f"✅ Inference completed successfully!")
    else:
        print(f"\n❌ Inference failed after {total_time:.2f} seconds")


if __name__ == "__main__":
    main()
