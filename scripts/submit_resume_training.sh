#!/bin/bash

# Resume Training Script for GPT-2 Diffusion
# This script is specifically designed to resume training from an existing checkpoint
# Usage: ./submit_resume_training.sh <checkpoint_path>

# Check if checkpoint path is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <checkpoint_path>"
    echo "Example: $0 /work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    exit 1
fi

CHECKPOINT_PATH="$1"

# Validate checkpoint path
if [ ! -d "$CHECKPOINT_PATH" ]; then
    echo "Error: Checkpoint path does not exist: $CHECKPOINT_PATH"
    exit 1
fi

# Check for required model files
if [ ! -f "$CHECKPOINT_PATH/config.json" ]; then
    echo "Error: config.json not found in checkpoint path: $CHECKPOINT_PATH"
    exit 1
fi

echo "Resuming training from checkpoint: $CHECKPOINT_PATH"

# Set environment variables for resume training
export RESUME_TRAINING=true
export RESUME_CHECKPOINT_PATH="$CHECKPOINT_PATH"

# Submit the job
echo "Submitting resume training job..."
sbatch scripts/submit_diffusion_training.sh

echo "Job submitted successfully!"
echo "Monitor the job with: squeue -u $USER"
echo "Check logs in the current directory for job output"
