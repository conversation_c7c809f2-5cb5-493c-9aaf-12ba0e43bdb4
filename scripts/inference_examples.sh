#!/bin/bash

# Inference Examples and Usage Guide
# This script shows various ways to use the inference runners

echo "🧬 Protein Diffusion Inference Examples"
echo "======================================="

# Set model path (update this to your actual model path)
MODEL_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"

echo ""
echo "📋 1. List available configurations:"
echo "python scripts/inference_runner.py --list-configs"
echo ""

echo "🚀 2. Quick test (fast, minimal parameters):"
echo "python scripts/inference_runner.py --config quick_test"
echo ""

echo "⚖️ 3. Standard inference (balanced parameters):"
echo "python scripts/inference_runner.py --config standard"
echo ""

echo "⭐ 4. High quality inference (optimized for longer sequences):"
echo "python scripts/inference_runner.py --config high_quality"
echo ""

echo "🔧 5. Custom parameters (override any setting):"
echo "python scripts/inference_runner.py --config standard --num_sequences 5 --temperature 0.9"
echo ""

echo "📊 6. Compare different configurations:"
echo "python scripts/inference_runner.py --compare quick_test standard high_quality"
echo ""

echo "🔄 7. Batch inference (run multiple configs):"
echo "python scripts/inference_runner.py --batch standard high_quality conservative"
echo ""

echo "🎯 8. Fully custom inference:"
echo "python scripts/inference_runner.py --num_sequences 3 --max_length 1536 --temperature 0.75 --max_steps 200"
echo ""

echo "🔍 9. Debug mode with step printing:"
echo "python scripts/inference_runner.py --config quick_test --print_steps"
echo ""

echo "📁 10. Custom output prefix:"
echo "python scripts/inference_runner.py --config standard --output_prefix my_experiment"
echo ""

echo ""
echo "💡 Tips:"
echo "   - Use 'quick_test' for debugging and testing"
echo "   - Use 'standard' for general protein generation"
echo "   - Use 'high_quality' for longer, more complex sequences"
echo "   - Use 'conservative' for stable, reliable generation"
echo "   - Use 'exploratory' for diverse sequence exploration"
echo "   - Adjust temperature: lower (0.6-0.7) = more conservative, higher (0.8-0.9) = more diverse"
echo "   - Adjust max_steps: more steps = longer sequences but slower generation"
echo ""

echo "🎮 Interactive mode examples:"
echo ""

# Function to run example if user wants
run_example() {
    local description="$1"
    local command="$2"
    
    echo "Example: $description"
    echo "Command: $command"
    echo -n "Run this example? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "Running: $command"
        eval "$command"
        echo ""
        echo "Press Enter to continue..."
        read -r
    fi
    echo ""
}

# Check if model exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "⚠️  Model path does not exist: $MODEL_PATH"
    echo "Please update MODEL_PATH in this script or train a model first."
    exit 1
fi

# Ask if user wants to run interactive examples
echo -n "Would you like to run interactive examples? (y/N): "
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "🎮 Interactive Examples:"
    echo "========================"
    
    run_example "List available configurations" \
        "python scripts/inference_runner.py --list-configs"
    
    run_example "Quick test (1 sequence, fast)" \
        "python scripts/inference_runner.py --config quick_test"
    
    run_example "Compare configurations" \
        "python scripts/inference_runner.py --compare quick_test standard high_quality"
    
    run_example "Standard inference (3 sequences)" \
        "python scripts/inference_runner.py --config standard"
    
    echo "🎉 Interactive examples completed!"
else
    echo ""
    echo "💡 To run any example, copy and paste the commands above."
    echo "   Make sure to update the model path if needed."
fi

echo ""
echo "📚 For more options, run:"
echo "python scripts/inference_runner.py --help"
