#!/usr/bin/env python3
"""
Inference script for diffusion via reasoning model.
Generates protein sequences in the exact same format as training data.
Output matches val_data_restructured.json structure.
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

# Add the src directory to the path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from diffusion_via_reasoning.trajectory.framework import TrajectoryStep, apply_step


class ProteinDiffusionInferenceTrainingFormat:
    """Inference class for protein diffusion model that outputs in training data format."""
    
    def __init__(self, model_path: str, device: str = "cuda"):
        self.device = device
        self.model_path = model_path
        
        print(f"Loading model from {model_path}...")
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
        self.model.eval()
        
        # Set padding token if not set
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        print(f"Model loaded successfully on {device}")
    
    def parse_action(self, action_text: str) -> Optional[TrajectoryStep]:
        """Parse an action string into a TrajectoryStep with improved parsing."""
        action_text = action_text.strip()

        # Remove any extra content after the action (like "with ..." or "Current state...")
        # Split on common separators and take the first part
        for separator in [' with ', ' Current state', ' current state', "'", '"']:
            if separator in action_text:
                action_text = action_text.split(separator)[0].strip()

        # Parse different action formats
        # "add X at position Y"
        add_match = re.match(r"add\s+(\w)\s+at\s+position\s+(\d+)", action_text)
        if add_match:
            token = add_match.group(1)
            position = int(add_match.group(2))
            return TrajectoryStep(action="add", position=position, token=token)

        # "remove X from position Y"
        remove_match = re.match(r"remove\s+(\w)\s+from\s+position\s+(\d+)", action_text)
        if remove_match:
            token = remove_match.group(1)
            position = int(remove_match.group(2))
            return TrajectoryStep(action="remove", position=position, token=token)

        # "replace X at position Y with Z"
        replace_match = re.match(r"replace\s+(\w)\s+at\s+position\s+(\d+)\s+with\s+(\w)", action_text)
        if replace_match:
            old_token = replace_match.group(1)
            position = int(replace_match.group(2))
            new_token = replace_match.group(3)
            return TrajectoryStep(action="replace", position=position, token=new_token, replaced_token=old_token)

        return None
    
    def generate_reverse_process(self, max_new_tokens: int = 1024, temperature: float = 0.8,
                               top_p: float = 0.9, repetition_penalty: float = 1.1) -> Dict[str, Any]:
        """Generate a reverse process starting from empty string."""

        # Try different prompts to get better generation
        prompts = [
            "Diffusion reverse process:\nInitial state: ''\nadd M at position 0\nadd",
            "Diffusion reverse process:\nInitial state: ''\nadd M at position 0\nremove M from position 0\nadd",
            "Diffusion reverse process:\nInitial state: ''\nadd A at position 0\nadd T at position 1\nadd",
        ]

        best_result = None
        best_score = 0

        for prompt in prompts:
            # Tokenize prompt
            inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)

            # Generate text with more conservative parameters
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    repetition_penalty=repetition_penalty,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id,
                    # Add some constraints to improve generation
                    no_repeat_ngram_size=2,
                )

            # Decode generated text
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Parse and score this result
            result = self.parse_generated_process(generated_text)
            score = len(result["reverse_process"]) if result["success"] else 0

            if score > best_score:
                best_score = score
                best_result = result

        return best_result if best_result else {"reverse_process": [], "final_sequence": "", "generated_text": "", "success": False}
        
        # Decode generated text
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Parse the generated reverse process
        return self.parse_generated_process(generated_text)
    
    def parse_generated_process(self, generated_text: str) -> Dict[str, Any]:
        """Parse generated text into training data format with aggressive action extraction."""

        current_state = ""
        reverse_process = []
        actions_found = 0

        print(f"🔍 Parsing generated text: {repr(generated_text[:200])}...")

        # First, try to extract actions from the entire text using regex
        action_patterns = [
            r'add\s+([A-Z])\s+at\s+position\s+(\d+)',
            r'remove\s+([A-Z])\s+from\s+position\s+(\d+)',
            r'replace\s+([A-Z])\s+at\s+position\s+(\d+)\s+with\s+([A-Z])'
        ]

        all_actions = []

        # Extract all potential actions using regex
        for pattern in action_patterns:
            matches = re.finditer(pattern, generated_text, re.IGNORECASE)
            for match in matches:
                if 'add' in pattern:
                    action_text = f"add {match.group(1)} at position {match.group(2)}"
                    action = TrajectoryStep(action="add", position=int(match.group(2)), token=match.group(1))
                elif 'remove' in pattern:
                    action_text = f"remove {match.group(1)} from position {match.group(2)}"
                    action = TrajectoryStep(action="remove", position=int(match.group(2)), token=match.group(1))
                elif 'replace' in pattern:
                    action_text = f"replace {match.group(1)} at position {match.group(2)} with {match.group(3)}"
                    action = TrajectoryStep(action="replace", position=int(match.group(2)), token=match.group(3), replaced_token=match.group(1))

                all_actions.append((action, action_text, match.start()))

        # Sort actions by their position in the text
        all_actions.sort(key=lambda x: x[2])

        # Start with empty string
        reverse_process = [""]

        # Apply actions in order, but be robust to errors
        for action, action_text, _ in all_actions:
            try:
                new_state = apply_step(current_state, action)
                current_state = new_state
                actions_found += 1
                reverse_process.append(action_text)
                print(f"✅ Applied action {actions_found}: {action_text} -> state: '{current_state}'")

                # Stop if we get a reasonable length sequence
                if len(current_state) > 50:  # Reasonable protein length
                    break

            except Exception as e:
                print(f"❌ Error applying action '{action_text}': {e}")
                # Don't break, try the next action
                continue

        # If we didn't find any actions through regex, try line-by-line parsing
        if actions_found == 0:
            lines = generated_text.split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Skip prompt lines
                if line.startswith("Diffusion reverse process:") or line.startswith("Initial state:"):
                    continue

                # Try to parse this line as an action
                action = self.parse_action(line)
                if action:
                    try:
                        new_state = apply_step(current_state, action)
                        current_state = new_state
                        actions_found += 1
                        clean_action = line.split(' with ')[0].split(' Current state')[0].strip()
                        reverse_process.append(clean_action)
                        print(f"✅ Line-parsed action {actions_found}: {clean_action} -> state: '{current_state}'")
                    except Exception as e:
                        print(f"❌ Error applying line action '{line}': {e}")
                        continue

        print(f"📊 Parsing complete: {actions_found} actions found, final state: '{current_state}'")

        return {
            "reverse_process": reverse_process,
            "final_sequence": current_state,
            "generated_text": generated_text,
            "success": len(reverse_process) > 1 and len(current_state) > 0
        }
    
    def generate_multiple_sequences(self, num_sequences: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """Generate multiple protein sequences in training data format."""
        sequences = []
        
        print(f"Generating {num_sequences} protein sequences in training data format...")
        
        for i in range(num_sequences):
            print(f"\nGenerating sequence {i+1}/{num_sequences}...")
            result = self.generate_reverse_process(**kwargs)
            
            if result["success"]:
                print(f"✅ Generated sequence: {result['final_sequence']}")
                print(f"   Length: {len(result['final_sequence'])}")
                print(f"   Reverse process steps: {len(result['reverse_process']) - 1}")  # -1 for initial empty string
                
                # Show first few steps in training format
                print(f"   First few steps:")
                for j, step in enumerate(result["reverse_process"][:6]):
                    if j == 0:
                        print(f"      \"{step}\"")  # Empty string
                    else:
                        print(f"      \"{step}\"")
                if len(result["reverse_process"]) > 6:
                    print(f"      ... and {len(result['reverse_process']) - 6} more steps")
            else:
                print(f"❌ Failed to generate valid sequence")
            
            sequences.append(result)
        
        return sequences


def save_inference_results_training_format(results: List[Dict[str, Any]], output_file: str):
    """Save inference results in the exact same format as training data."""
    
    # Create output in the same format as val_data_restructured.json
    output_data = []
    
    for result in results:
        if result["success"]:
            # Match the exact structure of training data
            sequence_data = {
                "reverse_process": result["reverse_process"]
            }
            output_data.append(sequence_data)
    
    # Save to file
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\nInference results saved to {output_file} in training data format")
    print(f"Generated {len(output_data)} successful sequences")


def main():
    parser = argparse.ArgumentParser(description="Inference for protein diffusion model (training data format)")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to trained model directory")
    parser.add_argument("--num_sequences", type=int, default=5,
                       help="Number of sequences to generate")
    parser.add_argument("--max_new_tokens", type=int, default=1024,
                       help="Maximum new tokens to generate")
    parser.add_argument("--temperature", type=float, default=0.8,
                       help="Sampling temperature")
    parser.add_argument("--top_p", type=float, default=0.9,
                       help="Top-p sampling parameter")
    parser.add_argument("--repetition_penalty", type=float, default=1.1,
                       help="Repetition penalty")
    parser.add_argument("--output_file", type=str, default="inference_results_training_format.json",
                       help="Output file for results")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use (cuda/cpu)")
    
    args = parser.parse_args()
    
    # Check if model path exists
    if not os.path.exists(args.model_path):
        print(f"Error: Model path {args.model_path} does not exist!")
        return
    
    # Initialize inference
    try:
        inference = ProteinDiffusionInferenceTrainingFormat(args.model_path, args.device)
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Generate sequences
    results = inference.generate_multiple_sequences(
        num_sequences=args.num_sequences,
        max_new_tokens=args.max_new_tokens,
        temperature=args.temperature,
        top_p=args.top_p,
        repetition_penalty=args.repetition_penalty
    )
    
    # Save results in training data format
    save_inference_results_training_format(results, args.output_file)
    
    # Print summary
    successful = sum(1 for r in results if r["success"])
    print(f"\n🎯 Summary:")
    print(f"   Total attempts: {len(results)}")
    print(f"   Successful: {successful}")
    print(f"   Success rate: {successful/len(results)*100:.1f}%")
    
    if successful > 0:
        successful_results = [r for r in results if r["success"]]
        lengths = [len(r["final_sequence"]) for r in successful_results]
        
        print(f"   Average sequence length: {sum(lengths)/len(lengths):.1f}")
        
        print(f"\n📄 Sample sequences:")
        for i, result in enumerate(successful_results[:3]):
            print(f"   {i+1}. {result['final_sequence']}")


if __name__ == "__main__":
    main()
