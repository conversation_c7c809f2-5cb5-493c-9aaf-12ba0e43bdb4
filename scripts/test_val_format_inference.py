#!/usr/bin/env python3
"""
Test script to verify that inference output matches val_data_restructured.json format.
This script runs a quick inference test and compares the output structure.
"""

import os
import json
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from scripts.inference_diffusion import ProteinDiffusionInference


def load_val_data_sample():
    """Load a sample from val_data_restructured.json for comparison."""
    val_data_path = "data/CreateProtein_truly_uniform_crop/val_data_restructured.json"
    
    if not os.path.exists(val_data_path):
        print(f"❌ Val data file not found: {val_data_path}")
        return None
    
    with open(val_data_path, 'r') as f:
        val_data = json.load(f)
    
    # Return first sample for comparison
    if len(val_data) > 0:
        return val_data[0]
    return None


def analyze_structure(data, name):
    """Analyze the structure of a reverse process."""
    print(f"\n📊 Analyzing {name}:")
    
    if "reverse_process" not in data:
        print("   ❌ Missing 'reverse_process' key")
        return
    
    reverse_process = data["reverse_process"]
    print(f"   Total items: {len(reverse_process)}")
    
    # Check if starts with empty string
    if len(reverse_process) > 0 and reverse_process[0] == "":
        print("   ✅ Starts with empty string")
    else:
        print(f"   ❌ Does not start with empty string, starts with: '{reverse_process[0] if reverse_process else 'N/A'}'")
    
    # Count actions vs current state lines
    action_count = 0
    state_count = 0
    
    for item in reverse_process:
        if item.startswith("Current state:"):
            state_count += 1
        elif item != "":  # Not empty string and not current state
            action_count += 1
    
    print(f"   Actions: {action_count}")
    print(f"   Current state lines: {state_count}")
    
    # Show first few items
    print("   First 10 items:")
    for i, item in enumerate(reverse_process[:10]):
        if item == "":
            print(f"     {i+1}. [empty string]")
        else:
            print(f"     {i+1}. {item}")
    
    # Show some current state examples
    state_examples = [item for item in reverse_process if item.startswith("Current state:")]
    if state_examples:
        print(f"   Sample current states:")
        for i, state in enumerate(state_examples[:3]):
            print(f"     {i+1}. {state}")


def test_inference_format():
    """Test inference and compare format with val_data_restructured.json."""
    
    print("🧪 Testing inference output format...")
    
    # Load validation data sample
    val_sample = load_val_data_sample()
    if val_sample:
        analyze_structure(val_sample, "val_data_restructured.json sample")
    
    # Test inference
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        print("Please update the model path in the script.")
        return
    
    try:
        print(f"\n🔄 Loading model from: {model_path}")
        inference = ProteinDiffusionInference(model_path, device="cuda")
        print("✅ Model loaded successfully!")
        
        # Generate a test sequence
        print("\n🔄 Generating test sequence...")
        result = inference.generate_reverse_process(
            max_length=512,
            temperature=0.8,
            top_p=0.9,
            max_steps=50,  # Small for quick test
            print_steps=True
        )
        
        if result["success"]:
            print(f"\n✅ Generation successful!")
            print(f"   Final sequence: {result['final_sequence']}")
            print(f"   Steps: {result['num_steps']}")
            
            # Format like training data
            formatted_output = inference.format_output_like_training_data(result, include_step_info=True)
            
            # Create test output structure
            test_output = {
                "reverse_process": formatted_output
            }
            
            # Analyze the generated structure
            analyze_structure(test_output, "Generated inference output")
            
            # Save test output
            output_file = "test_val_format_output.json"
            with open(output_file, 'w') as f:
                json.dump([test_output], f, indent=2)
            
            print(f"\n💾 Test output saved to: {output_file}")
            
            # Compare structures
            print(f"\n🔍 Structure Comparison:")
            if val_sample:
                val_actions = len([item for item in val_sample["reverse_process"] if item != "" and not item.startswith("Current state:")])
                val_states = len([item for item in val_sample["reverse_process"] if item.startswith("Current state:")])
                
                test_actions = len([item for item in formatted_output if item != "" and not item.startswith("Current state:")])
                test_states = len([item for item in formatted_output if item.startswith("Current state:")])
                
                print(f"   Val data - Actions: {val_actions}, States: {val_states}")
                print(f"   Test data - Actions: {test_actions}, States: {test_states}")
                
                if formatted_output[0] == "" and val_sample["reverse_process"][0] == "":
                    print("   ✅ Both start with empty string")
                else:
                    print("   ❌ Different starting points")
            
        else:
            print(f"❌ Generation failed")
            print(f"   Generated text preview: {result['generated_text'][:200]}...")
            
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_inference_format()
