#!/usr/bin/env python3
"""
Debug version of training format inference to see what's being generated.
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM

# Add the src directory to the path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from diffusion_via_reasoning.trajectory.framework import TrajectoryStep, apply_step


def parse_action(action_text: str) -> Optional[TrajectoryStep]:
    """Parse an action string into a TrajectoryStep."""
    action_text = action_text.strip()
    
    # Parse different action formats
    # "add X at position Y"
    add_match = re.match(r"add\s+(\w)\s+at\s+position\s+(\d+)", action_text)
    if add_match:
        token = add_match.group(1)
        position = int(add_match.group(2))
        return TrajectoryStep(action="add", position=position, token=token)
    
    # "remove X from position Y"
    remove_match = re.match(r"remove\s+(\w)\s+from\s+position\s+(\d+)", action_text)
    if remove_match:
        token = remove_match.group(1)
        position = int(remove_match.group(2))
        return TrajectoryStep(action="remove", position=position, token=token)
    
    # "replace X at position Y with Z"
    replace_match = re.match(r"replace\s+(\w)\s+at\s+position\s+(\d+)\s+with\s+(\w)", action_text)
    if replace_match:
        old_token = replace_match.group(1)
        position = int(replace_match.group(2))
        new_token = replace_match.group(3)
        return TrajectoryStep(action="replace", position=position, token=new_token, replaced_token=old_token)
    
    return None


def debug_inference(model_path: str, device: str = "cuda"):
    """Debug inference to see what's being generated."""
    
    print(f"🔍 Loading model from {model_path}...")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
    model.eval()
    
    # Set padding token if not set
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print(f"✅ Model loaded successfully on {device}")
    
    # Test different prompts
    prompts = [
        "Diffusion reverse process:\nInitial state: ''",
        "Diffusion reverse process:\nInitial state: ''\nadd",
        "Diffusion reverse process:\nInitial state: ''\nadd M at position 0",
    ]
    
    for i, prompt in enumerate(prompts):
        print(f"\n🧪 Test {i+1}: Prompt = '{prompt}'")
        print("-" * 50)
        
        # Tokenize prompt
        inputs = tokenizer(prompt, return_tensors="pt").to(device)
        print(f"Input tokens: {inputs['input_ids'].shape[1]}")
        
        # Generate with different parameters
        for temp in [0.7, 0.8, 1.0]:
            print(f"\n  Temperature {temp}:")
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=100,  # Short generation for debugging
                    temperature=temp,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )
            
            # Decode generated text
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            print(f"    Generated: {repr(generated_text)}")
            
            # Try to parse actions
            lines = generated_text.split('\n')
            actions_found = 0
            for line in lines:
                line = line.strip()
                if line and not line.startswith("Diffusion reverse process:") and not line.startswith("Initial state:"):
                    action = parse_action(line)
                    if action:
                        actions_found += 1
                        print(f"    ✅ Valid action: {line}")
                    else:
                        print(f"    ❌ Invalid line: {repr(line)}")
            
            print(f"    Actions found: {actions_found}")


def test_simple_generation(model_path: str, device: str = "cuda"):
    """Test very simple generation to see if model works at all."""
    
    print(f"\n🔬 Simple generation test...")
    
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
    model.eval()
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Very simple prompt
    prompt = "add"
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=50,
            temperature=0.8,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
        )
    
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"Simple prompt '{prompt}' -> {repr(generated_text)}")


def main():
    parser = argparse.ArgumentParser(description="Debug training format inference")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to trained model directory")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use (cuda/cpu)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model_path):
        print(f"❌ Model path does not exist: {args.model_path}")
        return
    
    try:
        # Test simple generation first
        test_simple_generation(args.model_path, args.device)
        
        # Then debug inference
        debug_inference(args.model_path, args.device)
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
