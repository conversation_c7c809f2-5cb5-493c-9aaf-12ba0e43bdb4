#!/usr/bin/env python3
"""
Simple test script to debug generation issues.
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import time

def test_simple_generation():
    """Test basic generation with different parameters."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    print("Loading model...")
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Test on CPU first to isolate GPU issues
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")
    
    if device == "cuda":
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto"
        )
    else:
        model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
    
    model.eval()
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Test prompts
    test_prompts = [
        '"reverse_process": [\n"",\n"add',
        '"reverse_process": ["", "add K at position 0", "add',
        'add K at position 0\nadd',
    ]
    
    for i, prompt in enumerate(test_prompts):
        print(f"\n🧪 Test {i+1}: Prompt = '{prompt[:50]}...'")
        
        inputs = tokenizer(prompt, return_tensors="pt").to(device)
        print(f"   Input tokens: {inputs['input_ids'].shape[1]}")
        
        # Try generation with very simple parameters
        try:
            start_time = time.time()
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=20,  # Very short for testing
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                )
            
            generation_time = time.time() - start_time
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"   ✅ Generation completed in {generation_time:.2f}s")
            print(f"   Generated: {generated_text}")
            
        except Exception as e:
            print(f"   ❌ Generation failed: {e}")
    
    # Test with longer generation
    print("\n🧪 Testing longer generation...")
    prompt = '"reverse_process": [\n"",\n"add'
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    
    for max_tokens in [50, 100, 150]:
        print(f"\n   Testing max_new_tokens={max_tokens}...")
        try:
            start_time = time.time()
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=0.8,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    repetition_penalty=1.1,
                )
            
            generation_time = time.time() - start_time
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            print(f"   ✅ Completed in {generation_time:.2f}s")
            print(f"   Generated length: {len(generated_text)} chars")
            
            # Count how many "add/remove/replace" actions were generated
            actions = generated_text.count("add ") + generated_text.count("remove ") + generated_text.count("replace ")
            print(f"   Actions found: {actions}")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            break

if __name__ == "__main__":
    test_simple_generation() 