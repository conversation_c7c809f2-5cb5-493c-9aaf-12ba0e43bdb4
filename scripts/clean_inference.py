#!/usr/bin/env python3
"""
Clean inference script with improved prompt and parsing.
"""

import os
import sys
import json
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from scripts.inference_diffusion import ProteinDiffusionInference, save_inference_results


def run_clean_inference():
    """Run inference with clean, improved settings."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return
    
    print("🧬 Clean Protein Inference")
    print("=" * 40)
    
    try:
        # Initialize inference
        print("Loading model...")
        inference = ProteinDiffusionInference(model_path, device="cuda")
        print("✅ Model loaded!")
        
        # Run inference with conservative parameters
        print("\n🔄 Generating sequences...")
        results = inference.generate_multiple_sequences(
            num_sequences=3,
            max_length=400,      # Conservative
            temperature=0.75,    # Conservative
            top_p=0.9,
            max_steps=50,        # Conservative
            print_steps=False
        )
        
        # Show results
        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 Results Summary:")
        print(f"   Total attempts: {len(results)}")
        print(f"   Successful: {successful}")
        print(f"   Success rate: {successful/len(results)*100:.1f}%")
        
        if successful > 0:
            print(f"\n🧬 Generated sequences:")
            for i, result in enumerate(results):
                if result["success"]:
                    seq = result['final_sequence']
                    print(f"   {i+1}. {seq} (length: {len(seq)}, steps: {result['num_steps']})")
            
            # Save results
            output_file = "clean_inference_results.json"
            save_inference_results(results, output_file, inference)
            print(f"\n💾 Results saved to: {output_file}")
        else:
            print(f"\n❌ No successful sequences generated")
            # Show debug info for first failed result
            if results:
                first_result = results[0]
                print(f"\n🔍 Debug info for first attempt:")
                print(f"   Generated text: {first_result.get('generated_text', '')[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_clean_inference()
