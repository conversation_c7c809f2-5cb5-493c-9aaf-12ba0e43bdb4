# Progressive Inference for Protein Diffusion

This document explains how to use the progressive inference feature to generate longer protein sequences that exceed the model's training length limitations.

## What is Progressive Inference?

Progressive inference is a technique that allows generating sequences longer than the model's maximum training length by:

1. **Segmented Generation**: Breaking the generation process into smaller segments
2. **Continuation**: Using the current state as a starting point for the next segment
3. **Accumulation**: Combining all segments into a final long sequence

## Why Use Progressive Inference?

- **Overcome Length Limits**: Generate sequences longer than the model's `max_position_embeddings`
- **Avoid CUDA Errors**: Prevent memory and indexing issues with very long sequences
- **Maintain Quality**: Each segment is generated within the model's optimal range

## Usage

### Basic Progressive Inference

```bash
python scripts/inference_diffusion.py \
    --model_path "/path/to/model" \
    --progressive \
    --target_protein_length 150 \
    --segment_length 2048 \
    --num_sequences 3 \
    --output_file "long_proteins.json"
```

### Parameters

#### Required for Progressive Mode:
- `--progressive`: Enable progressive generation
- `--target_protein_length`: Target length for final protein sequence

#### Optional Progressive Parameters:
- `--segment_length`: Maximum tokens per segment (default: 2048)
- `--max_steps`: Maximum diffusion steps per segment (default: 400)

#### Standard Parameters:
- `--num_sequences`: Number of sequences to generate
- `--temperature`: Sampling temperature (0.1-1.0)
- `--top_p`: Top-p sampling parameter
- `--device`: cuda/cpu

### Example Commands

#### Generate 100-amino acid proteins:
```bash
python scripts/inference_diffusion.py \
    --model_path "$MODEL_PATH" \
    --progressive \
    --target_protein_length 100 \
    --num_sequences 5 \
    --temperature 0.8 \
    --device cuda
```

#### Generate very long proteins (200+ amino acids):
```bash
python scripts/inference_diffusion.py \
    --model_path "$MODEL_PATH" \
    --progressive \
    --target_protein_length 250 \
    --segment_length 1536 \
    --num_sequences 2 \
    --temperature 0.7 \
    --device cuda
```

#### Safe CPU generation:
```bash
python scripts/inference_diffusion.py \
    --model_path "$MODEL_PATH" \
    --progressive \
    --target_protein_length 80 \
    --segment_length 1024 \
    --num_sequences 3 \
    --device cpu
```

## How It Works

1. **Initial Segment**: Start with empty string, generate first segment
2. **Continuation**: Use current protein state as starting point for next segment
3. **Iteration**: Repeat until target length is reached
4. **Combination**: Merge all steps and states into final result

### Example Process:
```
Segment 1: "" → "MKLAVFGHI..." (25 amino acids)
Segment 2: "MKLAVFGHI..." → "MKLAVFGHI...DEFPQR..." (50 amino acids)  
Segment 3: "MKLAVFGHI...DEFPQR..." → "MKLAVFGHI...DEFPQR...STUVWX" (75 amino acids)
```

## Output Format

Progressive inference produces the same output format as regular inference, with additional fields:

```json
{
  "generated_text": "Combined text from all segments",
  "final_sequence": "MKLAVFGHI...",
  "steps": [...],  // All steps from all segments
  "states": [...], // All intermediate states
  "num_steps": 150,
  "success": true,
  "progressive_segments": 3  // Number of segments used
}
```

## Tips and Best Practices

### Recommended Parameters:
- **segment_length**: 1536-2048 (balance between efficiency and safety)
- **target_protein_length**: 50-200 (depending on your needs)
- **temperature**: 0.7-0.9 (higher for more diversity)

### Troubleshooting:
- **CUDA errors**: Reduce `segment_length` or use `--device cpu`
- **Poor quality**: Lower `temperature` or reduce `target_protein_length`
- **Slow generation**: Increase `segment_length` (if memory allows)

### Safety Limits:
- Maximum 10 segments per sequence (prevents infinite loops)
- Automatic fallback if segment generation fails
- Validation of each segment before continuation

## Comparison: Regular vs Progressive

| Aspect | Regular Inference | Progressive Inference |
|--------|------------------|----------------------|
| Max Length | ~50-100 amino acids | 200+ amino acids |
| Memory Usage | Fixed | Scales with segments |
| Generation Time | Fast | Slower (multiple segments) |
| Quality | High | Good (may have segment boundaries) |
| CUDA Errors | Possible with long sequences | Rare |

## Running the Example

Use the provided script for quick testing:

```bash
./run_progressive_inference.sh
```

This will generate 3 protein sequences of ~100 amino acids each using progressive inference.
