#!/bin/bash

# Optimized training parameters incorporating all fixes and latest optimizations
log "Setting up training parameters..."
DATA_DIR="./data/CreateProtein_truly_uniform_crop"
BASE_OUTPUT_DIR="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs"
MODEL_NAME="gpt2"

# Latest optimized parameters for ultra-long sequences (up to 15k tokens)
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=2
LEARNING_RATE=2e-5
NUM_EPOCHS=2000
MAX_CONTEXT_LENGTH=4096
SAVE_STEPS=4
LOGGING_STEPS=2

# Create output directory if it doesn't exist
log "Creating output directory..."
mkdir -p $BASE_OUTPUT_DIR || error_exit "Failed to create output directory"

# Set environment variables for distributed training and stability
log "Setting up environment variables..."
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false
export NCCL_DEBUG=WARN
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1

# Enhanced CUDA debugging and memory management
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Disable problematic features for long sequences
export TORCH_CUDNN_ENABLED=false
export PYTORCH_DISABLE_CUDNN_ATTENTION=1

# W&B configuration
export WANDB_PROJECT="Diffusion_via_Reasoning"
export WANDB_CACHE_DIR="/u/dzhang5/Diffusion_via_Reasoning/wandb"

# Run validation checks identical to SLURM version
# ... [validation code omitted for brevity] ...

# Start training with torchrun (equivalent to SLURM version)
log "Starting training with torchrun..."
torchrun \
    --standalone \
    --nproc_per_node=4 \
    scripts/train_gpt2_diffusion.py \
    --data_dir $DATA_DIR \
    --base_output_dir $BASE_OUTPUT_DIR \
    --llm_model_name_or_path $MODEL_NAME \
    --per_device_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --max_context_length $MAX_CONTEXT_LENGTH \
    --save_steps $SAVE_STEPS \
    --logging_steps $LOGGING_STEPS \
    --wandb_logging \
    --gradient_checkpointing \
    --ddp_backend gloo \
    --dataloader_num_workers 2 \
    --save_total_limit 10

TRAINING_EXIT_CODE=$?

# Post-training analysis and cleanup
if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    log "Training completed successfully!"
else
    log "Training failed with exit code: $TRAINING_EXIT_CODE"
fi

log "Job completed at $(date)"
log "Total runtime: $SECONDS seconds"