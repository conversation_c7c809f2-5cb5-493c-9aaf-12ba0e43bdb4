#!/usr/bin/env python3
"""
Test script for progressive inference functionality.
"""

import sys
from pathlib import Path

# Add src to path
ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT / "src"))

def test_progressive_inference():
    """Test progressive inference with mock data."""
    
    from scripts.inference_diffusion import ProteinDiffusionInference
    
    # Create a mock inference class for testing
    class MockProgressiveInference(ProteinDiffusionInference):
        def __init__(self):
            # Skip model loading for testing
            pass
        
        def generate_reverse_process(self, **kwargs):
            """Mock generation that simulates progressive behavior."""
            progressive = kwargs.get('progressive', False)
            target_protein_length = kwargs.get('target_protein_length', 50)
            
            if progressive:
                print(f"🔄 Mock progressive generation (target: {target_protein_length})")
                
                # Simulate progressive generation
                segments = []
                current_length = 0
                segment_count = 0
                
                while current_length < target_protein_length and segment_count < 3:
                    segment_count += 1
                    # Simulate generating 20-30 characters per segment
                    segment_length = min(25, target_protein_length - current_length)
                    segment = "M" * segment_length  # Mock protein sequence
                    segments.append(segment)
                    current_length += segment_length
                    print(f"  📝 Mock segment {segment_count}: +{segment_length} chars (total: {current_length})")
                
                final_sequence = "".join(segments)
                
                return {
                    "generated_text": f"Mock progressive generation: {final_sequence}",
                    "final_sequence": final_sequence,
                    "steps": [{"step": i+1, "action": f"add M at position {i}", "state": "M"*(i+1)} 
                             for i in range(len(final_sequence))],
                    "states": [""] + ["M"*(i+1) for i in range(len(final_sequence))],
                    "num_steps": len(final_sequence),
                    "success": True,
                    "progressive_segments": segment_count
                }
            else:
                # Regular generation
                sequence = "MKLAVF"  # Mock short sequence
                return {
                    "generated_text": f"Mock regular generation: {sequence}",
                    "final_sequence": sequence,
                    "steps": [{"step": i+1, "action": f"add {sequence[i]} at position {i}", "state": sequence[:i+1]} 
                             for i in range(len(sequence))],
                    "states": [""] + [sequence[:i+1] for i in range(len(sequence))],
                    "num_steps": len(sequence),
                    "success": True
                }
    
    # Test regular generation
    print("=== Test 1: Regular Generation ===")
    inference = MockProgressiveInference()
    result1 = inference.generate_reverse_process(max_length=512)
    print(f"Result: {result1['final_sequence']} (length: {len(result1['final_sequence'])})")
    print(f"Steps: {result1['num_steps']}")
    print()
    
    # Test progressive generation
    print("=== Test 2: Progressive Generation ===")
    result2 = inference.generate_reverse_process(
        progressive=True, 
        target_protein_length=60,
        segment_length=2048
    )
    print(f"Result: {result2['final_sequence']} (length: {len(result2['final_sequence'])})")
    print(f"Steps: {result2['num_steps']}")
    print(f"Segments: {result2.get('progressive_segments', 'N/A')}")
    print()
    
    # Test multiple sequences
    print("=== Test 3: Multiple Progressive Sequences ===")
    results = inference.generate_multiple_sequences(
        num_sequences=2,
        progressive=True,
        target_protein_length=40
    )
    
    for i, result in enumerate(results):
        print(f"Sequence {i+1}: {result['final_sequence']} (length: {len(result['final_sequence'])})")
    
    print("\n✅ All tests completed successfully!")

if __name__ == "__main__":
    test_progressive_inference()
