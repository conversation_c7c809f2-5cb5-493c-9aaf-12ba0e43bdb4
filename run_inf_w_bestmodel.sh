#!/bin/bash
MODEL_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model"

if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model path does not exist: $MODEL_PATH"
    exit 1
fi

source /u/dzhang5/miniforge3/etc/profile.d/conda.sh
conda activate diffusion_via_reasoning

python scripts/inference_wo_stp.py \
    --model_path "$MODEL_PATH" \
    --num_sequences 3 \
    --max_length 4024 \
    --min_length 512 \
    --temperature 0.8 \
    --top_p 0.9 \
    --output protein_inference_results_best_model.json \
    --device cuda

echo "Inference completed! Results saved to protein_inference_results_best_model.json"
