# Training Format Inference Guide

本指南介绍如何使用 `inference_diffusion_training_format.py` 生成与训练数据格式完全一致的蛋白质序列。

## 快速开始

### 方法1：使用便捷脚本（推荐）

```bash
# 1. 更新脚本中的模型路径
# 编辑 scripts/run_training_format_inference.sh，修改 MODEL_PATH 变量

# 2. 运行脚本
bash scripts/run_training_format_inference.sh
```

### 方法2：直接使用Python脚本

```bash
# 基本使用
python scripts/inference_diffusion_training_format.py \
    --model_path /path/to/your/model \
    --num_sequences 3 \
    --output_file my_results.json

# GPU推理（推荐）
python scripts/inference_diffusion_training_format.py \
    --model_path /path/to/your/best_model \
    --num_sequences 5 \
    --max_new_tokens 1024 \
    --temperature 0.8 \
    --device cuda

# CPU推理
python scripts/inference_diffusion_training_format.py \
    --model_path ./test_output/checkpoint-5 \
    --num_sequences 2 \
    --device cpu
```

## 参数说明

### 必需参数
- `--model_path`: 训练好的模型目录路径

### 可选参数
- `--num_sequences`: 生成序列数量（默认：5）
- `--max_new_tokens`: 最大生成token数（默认：1024）
- `--temperature`: 采样温度（默认：0.8）
- `--top_p`: Top-p采样参数（默认：0.9）
- `--repetition_penalty`: 重复惩罚（默认：1.1）
- `--output_file`: 输出文件名（默认：inference_results_training_format.json）
- `--device`: 使用设备（默认：cuda）

## 使用示例

### 示例1：快速测试
```bash
python scripts/inference_diffusion_training_format.py \
    --model_path ./test_output/checkpoint-5 \
    --num_sequences 2 \
    --max_new_tokens 512 \
    --device cpu \
    --output_file quick_test.json
```

### 示例2：标准GPU推理
```bash
python scripts/inference_diffusion_training_format.py \
    --model_path /path/to/your/best_model \
    --num_sequences 5 \
    --max_new_tokens 1024 \
    --temperature 0.8 \
    --top_p 0.9 \
    --device cuda \
    --output_file standard_results.json
```

### 示例3：保守参数生成
```bash
python scripts/inference_diffusion_training_format.py \
    --model_path /path/to/your/model \
    --num_sequences 3 \
    --max_new_tokens 800 \
    --temperature 0.7 \
    --top_p 0.85 \
    --repetition_penalty 1.2 \
    --output_file conservative_results.json
```

### 示例4：探索性生成
```bash
python scripts/inference_diffusion_training_format.py \
    --model_path /path/to/your/model \
    --num_sequences 10 \
    --max_new_tokens 1536 \
    --temperature 0.9 \
    --top_p 0.95 \
    --repetition_penalty 1.0 \
    --output_file exploratory_results.json
```

## 输出格式

生成的JSON文件格式与训练数据完全一致：

```json
[
  {
    "reverse_process": [
      "",
      "add M at position 0",
      "add K at position 1",
      "Current state: 'MK'",
      "add V at position 2",
      "add L at position 3",
      "replace K at position 1 with A",
      "Current state: 'MAVL'",
      ...
    ]
  },
  {
    "reverse_process": [
      "",
      "add A at position 0",
      ...
    ]
  }
]
```

## 与原版本的区别

| 特性 | `inference_diffusion.py` | `inference_diffusion_training_format.py` |
|------|--------------------------|-------------------------------------------|
| 输出格式 | 包含"Step 1:", "Step 2:"等前缀 | 与训练数据完全一致，无Step前缀 |
| 数据结构 | 包含step、action、state字段 | 只有reverse_process数组 |
| 用途 | 调试和分析推理过程 | 生成可用作训练数据的格式 |
| 兼容性 | 适合查看推理步骤 | 直接兼容val_data_restructured.json |

## 推荐参数设置

### 调试和测试
```bash
--num_sequences 1 --max_new_tokens 512 --temperature 0.8 --device cpu
```

### 标准使用
```bash
--num_sequences 3 --max_new_tokens 1024 --temperature 0.8 --device cuda
```

### 高质量生成
```bash
--num_sequences 2 --max_new_tokens 1536 --temperature 0.7 --top_p 0.95
```

### 批量生成
```bash
--num_sequences 10 --max_new_tokens 800 --temperature 0.8 --repetition_penalty 1.1
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少序列数量或token数
   --num_sequences 1 --max_new_tokens 512
   ```

2. **生成质量不佳**
   ```bash
   # 调整温度和重复惩罚
   --temperature 0.7 --repetition_penalty 1.2
   ```

3. **生成速度慢**
   ```bash
   # 使用GPU或减少参数
   --device cuda --max_new_tokens 512
   ```

4. **模型路径错误**
   ```bash
   # 检查模型文件是否存在
   ls /path/to/your/model/
   # 应该包含：config.json, tokenizer.json, model.safetensors
   ```

## 验证输出

生成结果后，可以与训练数据进行比较：

```bash
# 查看训练数据格式
head -50 data/CreateProtein_truly_uniform_crop/val_data_restructured.json

# 查看生成结果格式
head -50 your_output_file.json

# 验证格式一致性
python -c "
import json
with open('your_output_file.json', 'r') as f:
    data = json.load(f)
print('Generated sequences:', len(data))
print('First sequence steps:', len(data[0]['reverse_process']))
print('Format check: OK' if 'reverse_process' in data[0] else 'Format error')
"
```

这样您就可以生成与训练数据格式完全一致的蛋白质序列了！
