#!/bin/bash

# Progressive Inference Script for Protein Diffusion Model
# This script generates longer protein sequences using progressive generation

# Set model path
MODEL_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model"

# Check if model exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model path does not exist: $MODEL_PATH"
    exit 1
fi

echo "Running progressive inference with model: $MODEL_PATH"

# Activate conda environment
source /u/dzhang5/miniforge3/etc/profile.d/conda.sh
conda activate diffusion_via_reasoning

# Run progressive inference
python scripts/inference_diffusion.py \
    --model_path "$MODEL_PATH" \
    --num_sequences 3 \
    --progressive \
    --target_protein_length 600 \
    --segment_length 12048 \
    --temperature 0.8 \
    --top_p 0.9 \
    --max_steps 400 \
    --output_file "progressive_inference_results.json" \
    --device "cuda"

echo "Progressive inference completed! Results saved to progressive_inference_results.json"
