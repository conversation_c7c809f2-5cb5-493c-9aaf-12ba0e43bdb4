# Resume Training Guide

This guide explains how to resume training from an existing checkpoint using the updated training scripts.

## Overview

The training scripts now support resuming training from any saved checkpoint or best model. This is useful when:
- Training was interrupted
- You want to continue training with different parameters
- You want to fine-tune a previously trained model

## Updated Scripts

### 1. `submit_diffusion_training.sh` (Updated)
The main training script now supports resume parameters:
- Added `RESUME_TRAINING` and `RESUME_CHECKPOINT_PATH` environment variables
- Automatically detects and validates checkpoint paths
- Preserves existing output directory structure when resuming

### 2. `submit_resume_training.sh` (New)
A convenient wrapper script specifically for resuming training:
```bash
./scripts/submit_resume_training.sh <checkpoint_path>
```

## Usage Examples

### Method 1: Using the Resume Script (Recommended)
```bash
# Resume from best model
./scripts/submit_resume_training.sh "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"

# Resume from specific checkpoint
./scripts/submit_resume_training.sh "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/checkpoint-3244/"
```

### Method 2: Using Environment Variables
```bash
# Set environment variables
export RESUME_TRAINING=true
export RESUME_CHECKPOINT_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"

# Submit training job
sbatch scripts/submit_diffusion_training.sh
```

### Method 3: Editing the Script Directly
Edit `scripts/submit_diffusion_training.sh` and uncomment/modify these lines:
```bash
# Configuration for resuming training (set these variables to enable resume)
RESUME_TRAINING=true
RESUME_CHECKPOINT_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
```

## Your Specific Case

For your model, you have two main options:

### Option 1: Resume from Complete Checkpoint (Recommended)
This preserves the exact training state including optimizer state:
```bash
./scripts/submit_resume_training.sh "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/checkpoint-3244/"
```

### Option 2: Resume from Best Model
This starts fresh training with the best model weights:
```bash
./scripts/submit_resume_training.sh "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/"
```

**Recommendation**: Use Option 1 (checkpoint-3244) for true continuation of training.

## What Happens When Resuming

1. **Model Loading**: The script loads the model weights, configuration, and tokenizer from the checkpoint
2. **Training State**: If resuming from a checkpoint (not best_model), it also restores optimizer state and training progress
3. **Output Directory**: Creates a new run with "_continue" suffix or uses the existing directory structure
4. **Validation**: Checks that the checkpoint path exists and contains required files

## Available Checkpoints

Your training run contains these options:
- `checkpoint-3244/` - Latest complete checkpoint (includes optimizer state, training state) - **Recommended for continuation**
- `checkpoint-3240/`, `checkpoint-3236/`, etc. - Earlier complete checkpoints
- `best_model/` - The model with the best validation loss (model weights only, no training state)

## Monitoring

After submitting the resume job:
```bash
# Check job status
squeue -u $USER

# Monitor logs
tail -f gpt2_diffusion_truly_uniform_crop_*.out

# Check for errors
tail -f gpt2_diffusion_truly_uniform_crop_*.err
```

## Notes

- **Complete Checkpoints** (`checkpoint-*/`): Include model weights, optimizer state, scheduler state, and training progress. Training continues exactly where it left off.
- **Best Model** (`best_model/`): Contains only the model weights with the best validation performance. Training starts fresh with new optimizer state.
- The script automatically handles tokenizer compatibility and position embedding extensions
- All original training parameters (batch size, learning rate, etc.) are preserved unless explicitly changed
- For true continuation of training, always use complete checkpoints (`checkpoint-*/`) rather than `best_model/`

## PyTorch Version Compatibility

Due to security vulnerability CVE-2025-32434, the script automatically handles PyTorch version compatibility:

- **PyTorch 2.6+**: Full checkpoint restoration including optimizer state
- **PyTorch < 2.6**: Loads model weights only, starts fresh training state for security

The script will automatically detect your PyTorch version and choose the appropriate loading method. You'll see messages like:
```
PyTorch 2.5.0 < 2.6, skipping optimizer state due to CVE-2025-32434
Starting fresh training with pre-loaded model weights...
```

This ensures your training can continue safely even with older PyTorch versions.
