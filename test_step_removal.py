#!/usr/bin/env python3
"""
Test script to verify that Step prefixes are removed from generated text.
"""

import sys
from pathlib import Path

# Add src to path
ROOT = Path(__file__).resolve().parent
sys.path.insert(0, str(ROOT / "src"))

from scripts.inference_wo_stp import ProteinDiffusionInference

def test_step_removal():
    """Test that Step prefixes are properly removed."""
    
    # Create a mock inference instance (we'll test the parsing directly)
    class MockInference(ProteinDiffusionInference):
        def __init__(self):
            # Skip model loading for testing
            pass
    
    inference = MockInference()
    
    # Test text with Step prefixes
    test_text = """Diffusion reverse process:
Initial state: ''
Step 1: add M at position 0
Step 2: add K at position 1
Current state: 'MK'
Step 3: add L at position 2
Step 4: replace K at position 1 with V
Current state: 'MVL'
Step 5: add F at position 3"""
    
    print("Original text:")
    print(test_text)
    print("\n" + "="*50 + "\n")
    
    # Parse the text
    result = inference._parse_reverse_text(test_text)
    
    print("Cleaned generated_text:")
    print(result["generated_text"])
    print("\n" + "="*50 + "\n")
    
    print("Final sequence:", result["final_sequence"])
    print("Number of steps:", result["num_steps"])
    print("Success:", result["success"])
    
    # Verify that Step prefixes are removed
    assert "Step 1:" not in result["generated_text"]
    assert "Step 2:" not in result["generated_text"]
    assert "add M at position 0" in result["generated_text"]
    assert "add K at position 1" in result["generated_text"]
    
    print("\n✅ Test passed! Step prefixes successfully removed.")

if __name__ == "__main__":
    test_step_removal()
