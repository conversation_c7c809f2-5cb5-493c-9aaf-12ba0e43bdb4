#!/bin/bash

# Inference script for your best model
# This script runs inference using your trained diffusion model

# Set model path
MODEL_PATH="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model"

# Check if model exists
if [ ! -d "$MODEL_PATH" ]; then
    echo "Error: Model path does not exist: $MODEL_PATH"
    exit 1
fi

echo "Running inference with model: $MODEL_PATH"

# Activate conda environment
source /u/dzhang5/miniforge3/etc/profile.d/conda.sh
conda activate diffusion_via_reasoning

# Run inference
python scripts/inference_diffusion.py \
    --model_path "$MODEL_PATH" \
    --num_sequences 3 \
    --max_length 4012 \
    --min_length 512 \
    --temperature 0.8 \
    --top_p 0.9 \
    --max_steps 400 \
    --output_file "protein_inference_results_best_model.json" \
    --device "cuda"

echo "Inference completed! Results saved to protein_inference_results_best_model.json"
