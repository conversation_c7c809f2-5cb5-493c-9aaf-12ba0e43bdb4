# Inference Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Issue 1: Generation Hangs/Freezes

**Symptoms:**
- <PERSON><PERSON><PERSON> stops at "⏳ Starting model.generate() with timeout..."
- No output for several minutes
- High GPU memory usage but no progress

**Solutions:**

#### Quick Fix - Use Safe Parameters
```bash
# Try ultra-safe parameters first
python scripts/run_safe_inference.py --config ultra_safe

# Or use the safe inference runner
python scripts/run_safe_inference.py
```

#### Parameter Adjustments
```bash
# Reduce temperature (lower = more conservative)
python scripts/inference_runner.py --temperature 0.7 --max_length 512

# Reduce max_length and max_steps
python scripts/inference_runner.py --max_length 768 --max_steps 50

# Use conservative config
python scripts/inference_runner.py --config conservative
```

#### Root Causes:
1. **High temperature (>0.85)**: Makes generation unpredictable
2. **Large max_length (>1500)**: Can cause memory issues
3. **Missing repetition penalty**: Can cause infinite loops
4. **Model architecture issues**: Some models are more prone to hanging

---

### Issue 2: Out of Memory Errors

**Symptoms:**
- CUDA out of memory errors
- System freezes
- "RuntimeError: CUDA out of memory"

**Solutions:**

```bash
# Reduce batch size and length
python scripts/inference_runner.py --num_sequences 1 --max_length 512

# Use CPU instead of GPU
python scripts/inference_runner.py --device cpu --config quick_test

# Clear GPU cache before running
python -c "import torch; torch.cuda.empty_cache()"
```

---

### Issue 3: Poor Quality Sequences

**Symptoms:**
- Very short sequences
- Repetitive patterns
- Invalid amino acids
- Nonsensical actions

**Solutions:**

```bash
# Use higher quality settings
python scripts/inference_runner.py --config high_quality

# Adjust temperature for better balance
python scripts/inference_runner.py --temperature 0.75 --top_p 0.95

# Enable step printing for debugging
python scripts/inference_runner.py --config standard --print_steps
```

---

### Issue 4: No Sequences Generated

**Symptoms:**
- "❌ Failed to generate valid sequence"
- Success rate: 0%
- Empty output files

**Solutions:**

```bash
# Check model path
ls -la /path/to/your/model/

# Try minimal parameters
python scripts/inference_runner.py --config quick_test --print_steps

# Check if model loads correctly
python scripts/test_new_inference.py
```

---

## 🛠️ Debugging Steps

### Step 1: Verify Setup
```bash
# Test basic functionality
python scripts/test_new_inference.py

# List available configurations
python scripts/inference_runner.py --list-configs

# Check model path
python scripts/inference_runner.py --config quick_test --print_steps
```

### Step 2: Try Safe Parameters
```bash
# Ultra-safe test
python scripts/run_safe_inference.py --config ultra_safe

# If that works, gradually increase complexity
python scripts/run_safe_inference.py --config safe
python scripts/run_safe_inference.py --config moderate
```

### Step 3: Parameter Tuning
```bash
# Start with working parameters and adjust one at a time
python scripts/inference_runner.py --config safe --temperature 0.8
python scripts/inference_runner.py --config safe --max_length 1024
python scripts/inference_runner.py --config safe --num_sequences 3
```

---

## 📊 Parameter Guidelines

### Temperature
- **0.6-0.7**: Very conservative, consistent results
- **0.7-0.8**: Balanced, recommended for most use cases
- **0.8-0.9**: More diverse, higher risk of hanging
- **>0.9**: High risk, use with caution

### Max Length
- **512**: Safe for testing
- **768-1024**: Good for standard proteins
- **1024-2048**: Longer proteins, higher memory usage
- **>2048**: High risk of hanging/memory issues

### Max Steps
- **30-50**: Quick generation, shorter sequences
- **50-100**: Balanced, good for most cases
- **100-200**: Longer sequences, more time
- **>200**: Very long sequences, high risk

---

## 🔧 Quick Fixes

### If Generation Hangs:
1. **Kill the process**: Ctrl+C
2. **Try safe parameters**: `python scripts/run_safe_inference.py --config ultra_safe`
3. **Reduce complexity**: Lower temperature, max_length, max_steps
4. **Check GPU memory**: `nvidia-smi`

### If Out of Memory:
1. **Reduce batch size**: `--num_sequences 1`
2. **Reduce length**: `--max_length 512`
3. **Use CPU**: `--device cpu`
4. **Clear cache**: `python -c "import torch; torch.cuda.empty_cache()"`

### If Poor Quality:
1. **Use proven configs**: `--config high_quality`
2. **Enable debugging**: `--print_steps`
3. **Adjust temperature**: `--temperature 0.75`
4. **Check training data format**

---

## 📞 Getting Help

### Debug Information to Collect:
```bash
# System info
nvidia-smi
python --version
pip list | grep torch

# Model info
ls -la /path/to/model/
python scripts/test_new_inference.py

# Error logs
python scripts/inference_runner.py --config quick_test --print_steps 2>&1 | tee debug.log
```

### Useful Commands:
```bash
# Monitor GPU usage
watch -n 1 nvidia-smi

# Check process status
ps aux | grep python

# Kill hanging processes
pkill -f inference_runner.py
```

---

## ✅ Recommended Workflow

1. **Start with safe parameters**:
   ```bash
   python scripts/run_safe_inference.py --config ultra_safe
   ```

2. **If successful, try standard**:
   ```bash
   python scripts/inference_runner.py --config standard
   ```

3. **Gradually increase complexity**:
   ```bash
   python scripts/inference_runner.py --config high_quality
   ```

4. **Fine-tune for your needs**:
   ```bash
   python scripts/inference_runner.py --config standard --temperature 0.85 --num_sequences 5
   ```

Remember: **Start conservative, then gradually increase complexity!**
