# 使用多个种子生成训练数据

## 概述

修改后的 `restructure_training_data.py` 脚本现在支持使用多个不同的随机种子来生成更多样化的训练数据。所有种子生成的训练数据会合并到一个train文件中，测试数据合并到一个test文件中，验证数据合并到一个validation文件中。

## 新增参数

- `--num-seeds`: 指定使用多少个不同的种子（默认为1）

## 使用方法

### 1. 基本用法 - 10个种子

```bash
python scripts/restructure_training_data.py \
    --num-seeds 10 \
    --output-dir ./data/CreateProtein_restructured_10seeds
```

### 2. 完整参数示例

```bash
python scripts/restructure_training_data.py \
    --fasta ./data/uniprot_sprot_head1000.fasta \
    --output-dir ./data/CreateProtein_restructured_10seeds \
    --train-ratio 0.7 \
    --test-ratio 0.1 \
    --val-ratio 0.2 \
    --seed 42 \
    --num-seeds 10 \
    --correct-action-prob 0.6 \
    --temperature 1.2
```

### 3. 使用预设脚本

```bash
# 完整参数版本
./scripts/run_restructure_with_multiple_seeds.sh

# 简化版本
./scripts/run_simple_10seeds.sh
```

## 输出格式

脚本会生成三个独立的JSON文件：

1. **train_data_restructured.json** - 包含所有种子的训练数据
2. **test_data_restructured.json** - 包含所有种子的测试数据
3. **val_data_restructured.json** - 包含所有种子的验证数据

每个文件的结构如下：

```json
[
  {
    "reverse_process": ["", "add A at position 0", "add C at position 1", ...]
  },
  {
    "reverse_process": ["", "add M at position 0", ...]
  },
  ...
]
```

## 种子机制

- 基础种子：通过 `--seed` 参数指定（默认42）
- 每个种子：`base_seed + seed_idx * 10000`
- 每个序列：`current_seed + sequence_idx`

例如，使用 `--seed 42 --num-seeds 3`：
- 种子1: 42, 43, 44, ... (对应序列0, 1, 2, ...)
- 种子2: 10042, 10043, 10044, ...
- 种子3: 20042, 20043, 20044, ...

## 数据量计算

如果原始数据有N个序列，使用K个种子，最终会生成：
- 训练数据：N × K × train_ratio 个样本
- 测试数据：N × K × test_ratio 个样本  
- 验证数据：N × K × val_ratio 个样本

例如：1000个序列 × 10个种子 = 10,000个训练样本（按比例分配）
