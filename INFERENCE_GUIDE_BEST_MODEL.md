# Inference Guide for Best Model

This guide shows how to use `scripts/inference_diffusion.py` with your trained best model for protein sequence generation.

## Model Path

Your best model is located at:
```
/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model/
```

## Quick Start

### Method 1: Using the Convenience Script (Recommended)
```bash
./run_inference_with_best_model.sh
```

### Method 2: Direct Command
```bash
# Activate environment
conda activate diffusion_via_reasoning

# Run inference
python scripts/inference_diffusion.py \
    --model_path "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model" \
    --num_sequences 5 \
    --max_length 1024 \
    --min_length 256 \
    --temperature 0.8 \
    --top_p 0.9 \
    --max_steps 200 \
    --output_file "protein_inference_results.json" \
    --device "cuda"
```

## Parameters Explanation

- `--model_path`: Path to your trained model directory
- `--num_sequences`: Number of protein sequences to generate (default: 5)
- `--max_length`: Maximum token length for generation (default: 512)
- `--min_length`: Minimum token length for generation (default: 256)
- `--temperature`: Sampling temperature (0.1-1.0, higher = more random)
- `--top_p`: Top-p sampling parameter (0.1-1.0)
- `--max_steps`: Maximum diffusion steps to attempt (default: 100)
- `--output_file`: Output JSON file for results
- `--device`: Device to use ("cuda" or "cpu")

## Recommended Settings

### For High-Quality Sequences (Conservative)
```bash
python scripts/inference_diffusion.py \
    --model_path "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model" \
    --num_sequences 3 \
    --max_length 2048 \
    --min_length 512 \
    --temperature 0.6 \
    --top_p 0.8 \
    --max_steps 300 \
    --output_file "conservative_results.json"
```

### For Diverse Sequences (Exploratory)
```bash
python scripts/inference_diffusion.py \
    --model_path "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model" \
    --num_sequences 10 \
    --max_length 1024 \
    --min_length 256 \
    --temperature 1.0 \
    --top_p 0.95 \
    --max_steps 200 \
    --output_file "diverse_results.json"
```

### For Quick Testing
```bash
python scripts/inference_diffusion.py \
    --model_path "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model" \
    --num_sequences 2 \
    --max_length 512 \
    --min_length 128 \
    --temperature 0.8 \
    --top_p 0.9 \
    --max_steps 100 \
    --output_file "quick_test.json"
```

## Output Format

The script generates a JSON file with the following structure:
```json
{
  "total_sequences": 5,
  "successful_sequences": 4,
  "sequences": [
    {
      "index": 1,
      "success": true,
      "final_sequence": "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUAIGLNLRSQTGSKTDTGDRYSDGTPVNYITPVLDGTYDSRYAFGRLFATVVEAHPDREIAIMQELQ",
      "sequence_length": 234,
      "num_steps": 45,
      "steps": [...],
      "full_generated_text": "..."
    }
  ]
}
```

## Expected Output

When running inference, you should see output like:
```
Loading model from /work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs/2025-06-18_23-46-35_GPT2_Diffusion_gpt2/best_model...
Model loaded successfully on cuda
Generating 5 protein sequences...

Generating sequence 1/5...
✅ Generated sequence: MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUAIGLNLRSQTGSKTDTGDRYSDGTPVNYITPVLDGTYDSRYAFGRLFATVVEAHPDREIAIMQELQ
   Length: 234
   Steps: 45

🎯 Summary:
   Total attempts: 5
   Successful: 4
   Success rate: 80.0%
   Average sequence length: 187.5
   Average steps: 42.3
```

## Troubleshooting

### GPU Memory Issues
If you encounter GPU memory issues, try:
- Reduce `--max_length` (e.g., 512 instead of 1024)
- Reduce `--num_sequences` (e.g., 1-2 instead of 5)
- Use `--device cpu` for CPU inference

### Model Loading Issues
- Ensure the model path exists and contains required files
- Check that you're in the correct conda environment
- Verify CUDA availability with `python -c "import torch; print(torch.cuda.is_available())"`

### Generation Quality Issues
- Adjust `--temperature` (lower for more conservative, higher for more diverse)
- Increase `--max_steps` for longer sequences
- Try different `--top_p` values

## Running on Different Devices

### GPU Inference (Recommended)
```bash
python scripts/inference_diffusion.py --model_path "..." --device cuda
```

### CPU Inference (Slower but works without GPU)
```bash
python scripts/inference_diffusion.py --model_path "..." --device cpu
```

## Next Steps

After generating sequences, you can:
1. Analyze the generated proteins using bioinformatics tools
2. Validate sequences using protein structure prediction
3. Compare with known protein databases
4. Use the sequences for downstream applications