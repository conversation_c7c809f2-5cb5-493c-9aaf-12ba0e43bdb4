# Protein Diffusion Inference Guide

This guide explains how to use the improved inference scripts for protein diffusion models.

## 🚀 Quick Start

### 1. Basic Inference
```bash
# Quick test (fast, 1 sequence)
python scripts/inference_runner.py --config quick_test

# Standard inference (3 sequences, balanced parameters)
python scripts/inference_runner.py --config standard

# High quality inference (longer sequences, optimized parameters)
python scripts/inference_runner.py --config high_quality
```

### 2. List Available Configurations
```bash
python scripts/inference_runner.py --list-configs
```

## 📋 Available Scripts

### 1. `scripts/inference_runner.py` (Recommended)
Advanced inference runner with configuration support.

**Features:**
- Pre-defined configuration presets
- Custom parameter override
- Batch processing
- Configuration comparison
- JSON configuration file support

### 2. `scripts/run_inference_optimized.py`
Simplified inference runner with built-in modes.

**Features:**
- Quick, standard, high_quality, and custom modes
- Built-in parameter presets
- Easy command-line interface

### 3. `scripts/inference_diffusion.py`
Core inference implementation (used by other scripts).

## 🔧 Configuration Presets

The system includes several pre-configured parameter sets:

| Config | Description | Sequences | Max Length | Temperature | Steps |
|--------|-------------|-----------|------------|-------------|-------|
| `quick_test` | Fast debugging | 1 | 512 | 0.8 | 50 |
| `standard` | Balanced quality/speed | 3 | 1024 | 0.8 | 150 |
| `high_quality` | Longer, higher quality | 2 | 2048 | 0.7 | 300 |
| `conservative` | Stable generation | 2 | 1024 | 0.6 | 200 |
| `exploratory` | Diverse sequences | 5 | 1536 | 0.9 | 250 |
| `long_sequences` | Very long proteins | 1 | 3072 | 0.75 | 400 |
| `batch_generation` | Many sequences quickly | 10 | 768 | 0.8 | 100 |

## 💡 Usage Examples

### Basic Usage
```bash
# Use a preset configuration
python scripts/inference_runner.py --config standard

# Custom parameters
python scripts/inference_runner.py --num_sequences 5 --temperature 0.9 --max_steps 200

# Override preset with custom parameters
python scripts/inference_runner.py --config standard --temperature 0.9 --num_sequences 5
```

### Advanced Usage
```bash
# Compare different configurations
python scripts/inference_runner.py --compare quick_test standard high_quality

# Batch inference with multiple configs
python scripts/inference_runner.py --batch standard high_quality conservative

# Custom output prefix
python scripts/inference_runner.py --config standard --output_prefix my_experiment

# Debug mode with step printing
python scripts/inference_runner.py --config quick_test --print_steps
```

### Interactive Examples
```bash
# Run interactive examples
bash scripts/inference_examples.sh
```

## 📊 Output Format

The inference scripts generate output in the same format as `val_data_restructured.json`:

```json
[
  {
    "reverse_process": [
      "",
      "add K at position 0",
      "add C at position 1",
      "add V at position 2",
      "Current state: 'KCV'",
      "replace C at position 1 with M",
      "Current state: 'KMV'",
      ...
    ]
  }
]
```

**Key features:**
- Starts with empty string `""`
- Contains action sequences
- Includes periodic `"Current state: 'sequence'"` updates
- Prioritizes actual states over computed states when they differ

## 🎛️ Parameter Tuning Guide

### Temperature
- **0.6-0.7**: Conservative, more consistent sequences
- **0.8**: Balanced (default)
- **0.9-1.0**: More diverse, exploratory sequences

### Max Steps
- **50-100**: Short sequences, fast generation
- **150-200**: Medium sequences (recommended)
- **300-400**: Long sequences, slower generation

### Max Length
- **512**: Quick tests
- **1024**: Standard protein sequences
- **2048+**: Long protein sequences

### Top-p
- **0.85**: Conservative sampling
- **0.9**: Balanced (default)
- **0.95**: More diverse sampling

## 🔍 Troubleshooting

### Model Loading Issues
```bash
# Check if model path exists
ls -la /path/to/your/model/

# Update model path in scripts or use --model_path
python scripts/inference_runner.py --model_path /your/model/path --config quick_test
```

### Generation Failures
```bash
# Use debug mode to see what's happening
python scripts/inference_runner.py --config quick_test --print_steps

# Try conservative parameters
python scripts/inference_runner.py --config conservative
```

### Memory Issues
```bash
# Use smaller parameters
python scripts/inference_runner.py --config quick_test

# Or reduce batch size
python scripts/inference_runner.py --num_sequences 1 --max_length 512
```

## 📁 File Structure

```
scripts/
├── inference_diffusion.py      # Core inference implementation
├── inference_runner.py         # Advanced runner (recommended)
├── run_inference_optimized.py  # Simplified runner
└── inference_examples.sh       # Interactive examples

configs/
└── inference_configs.json      # Configuration presets

outputs/
├── inference_*.json            # Generated sequences (val format)
└── inference_*_detailed.json   # Detailed results with metadata
```

## 🎯 Best Practices

1. **Start with quick_test** for debugging and validation
2. **Use standard config** for general protein generation
3. **Use high_quality config** for important sequences
4. **Monitor GPU memory** with longer sequences
5. **Save important results** with descriptive output prefixes
6. **Compare configurations** before large-scale generation
7. **Use batch processing** for systematic experiments

## 🔄 Model Path Configuration

Update the default model path in the scripts or use command line:

```bash
# Method 1: Command line
python scripts/inference_runner.py --model_path /your/model/path --config standard

# Method 2: Edit the default in scripts
# Update get_default_model_path() function in the scripts
```

## 📈 Performance Tips

- Use `quick_test` for rapid iteration
- Use GPU (`--device cuda`) for faster generation
- Batch multiple sequences rather than running separately
- Monitor generation time and adjust parameters accordingly
- Use `print_steps` only for debugging (slows down generation)

---

For more detailed options, run:
```bash
python scripts/inference_runner.py --help
```
